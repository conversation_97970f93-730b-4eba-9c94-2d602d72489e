(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function go(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const vo={},mo=()=>{},_o=Object.assign,yo=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},bo=Object.prototype.hasOwnProperty,On=(e,t)=>bo.call(e,t),xt=Array.isArray,Xt=e=>jn(e)==="[object Map]",wo=e=>jn(e)==="[object Set]",hr=e=>typeof e=="function",xo=e=>typeof e=="string",mn=e=>typeof e=="symbol",_n=e=>e!==null&&typeof e=="object",So=Object.prototype.toString,jn=e=>So.call(e),Co=e=>jn(e).slice(8,-1),Eo=e=>jn(e)==="[object Object]",Pr=e=>xo(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,St=(e,t)=>!Object.is(e,t),Ro=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let We;class Gs{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=We,!t&&We&&(this.index=(We.scopes||(We.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=We;try{return We=this,t()}finally{We=n}}}on(){We=this}off(){We=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function To(e){return new Gs(e)}function Po(){return We}let ge;const Qn=new WeakSet;class Vs{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,We&&We.active&&We.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Qn.has(this)&&(Qn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||qs(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Zr(this),Js(this);const t=ge,n=Qe;ge=this,Qe=!0;try{return this.fn()}finally{Qs(this),ge=t,Qe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Or(t);this.deps=this.depsTail=void 0,Zr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Qn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){dr(this)&&this.run()}get dirty(){return dr(this)}}let Ks=0,Zt,en;function qs(e,t=!1){if(e.flags|=8,t){e.next=en,en=e;return}e.next=Zt,Zt=e}function Ar(){Ks++}function Mr(){if(--Ks>0)return;if(en){let t=en;for(en=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Zt;){let t=Zt;for(Zt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Js(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Qs(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),Or(r),Ao(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function dr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ys(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ys(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===an))return;e.globalVersion=an;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!dr(e)){e.flags&=-3;return}const n=ge,r=Qe;ge=e,Qe=!0;try{Js(e);const s=e.fn(e._value);(t.version===0||St(s,e._value))&&(e._value=s,t.version++)}catch(s){throw t.version++,s}finally{ge=n,Qe=r,Qs(e),e.flags&=-3}}function Or(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Or(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ao(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Qe=!0;const Xs=[];function Ct(){Xs.push(Qe),Qe=!1}function Et(){const e=Xs.pop();Qe=e===void 0?!0:e}function Zr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ge;ge=void 0;try{t()}finally{ge=n}}}let an=0;class Mo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ir{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ge||!Qe||ge===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ge)n=this.activeLink=new Mo(ge,this),ge.deps?(n.prevDep=ge.depsTail,ge.depsTail.nextDep=n,ge.depsTail=n):ge.deps=ge.depsTail=n,Zs(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ge.depsTail,n.nextDep=void 0,ge.depsTail.nextDep=n,ge.depsTail=n,ge.deps===n&&(ge.deps=r)}return n}trigger(t){this.version++,an++,this.notify(t)}notify(t){Ar();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Mr()}}}function Zs(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Zs(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const pr=new WeakMap,Ot=Symbol(""),gr=Symbol(""),fn=Symbol("");function Ae(e,t,n){if(Qe&&ge){let r=pr.get(e);r||pr.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new Ir),s.map=r,s.key=n),s.track()}}function ut(e,t,n,r,s,i){const o=pr.get(e);if(!o){an++;return}const c=a=>{a&&a.trigger()};if(Ar(),t==="clear")o.forEach(c);else{const a=xt(e),p=a&&Pr(n);if(a&&n==="length"){const d=Number(r);o.forEach((_,y)=>{(y==="length"||y===fn||!mn(y)&&y>=d)&&c(_)})}else switch((n!==void 0||o.has(void 0))&&c(o.get(n)),p&&c(o.get(fn)),t){case"add":a?p&&c(o.get("length")):(c(o.get(Ot)),Xt(e)&&c(o.get(gr)));break;case"delete":a||(c(o.get(Ot)),Xt(e)&&c(o.get(gr)));break;case"set":Xt(e)&&c(o.get(Ot));break}}Mr()}function Ft(e){const t=le(e);return t===e?t:(Ae(t,"iterate",fn),Ye(e)?t:t.map(Ie))}function Lr(e){return Ae(e=le(e),"iterate",fn),e}const Oo={__proto__:null,[Symbol.iterator](){return Yn(this,Symbol.iterator,Ie)},concat(...e){return Ft(this).concat(...e.map(t=>xt(t)?Ft(t):t))},entries(){return Yn(this,"entries",e=>(e[1]=Ie(e[1]),e))},every(e,t){return lt(this,"every",e,t,void 0,arguments)},filter(e,t){return lt(this,"filter",e,t,n=>n.map(Ie),arguments)},find(e,t){return lt(this,"find",e,t,Ie,arguments)},findIndex(e,t){return lt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return lt(this,"findLast",e,t,Ie,arguments)},findLastIndex(e,t){return lt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return lt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Xn(this,"includes",e)},indexOf(...e){return Xn(this,"indexOf",e)},join(e){return Ft(this).join(e)},lastIndexOf(...e){return Xn(this,"lastIndexOf",e)},map(e,t){return lt(this,"map",e,t,void 0,arguments)},pop(){return qt(this,"pop")},push(...e){return qt(this,"push",e)},reduce(e,...t){return es(this,"reduce",e,t)},reduceRight(e,...t){return es(this,"reduceRight",e,t)},shift(){return qt(this,"shift")},some(e,t){return lt(this,"some",e,t,void 0,arguments)},splice(...e){return qt(this,"splice",e)},toReversed(){return Ft(this).toReversed()},toSorted(e){return Ft(this).toSorted(e)},toSpliced(...e){return Ft(this).toSpliced(...e)},unshift(...e){return qt(this,"unshift",e)},values(){return Yn(this,"values",Ie)}};function Yn(e,t,n){const r=Lr(e),s=r[t]();return r!==e&&!Ye(e)&&(s._next=s.next,s.next=()=>{const i=s._next();return i.value&&(i.value=n(i.value)),i}),s}const Io=Array.prototype;function lt(e,t,n,r,s,i){const o=Lr(e),c=o!==e&&!Ye(e),a=o[t];if(a!==Io[t]){const _=a.apply(e,i);return c?Ie(_):_}let p=n;o!==e&&(c?p=function(_,y){return n.call(this,Ie(_),y,e)}:n.length>2&&(p=function(_,y){return n.call(this,_,y,e)}));const d=a.call(o,p,r);return c&&s?s(d):d}function es(e,t,n,r){const s=Lr(e);let i=n;return s!==e&&(Ye(e)?n.length>3&&(i=function(o,c,a){return n.call(this,o,c,a,e)}):i=function(o,c,a){return n.call(this,o,Ie(c),a,e)}),s[t](i,...r)}function Xn(e,t,n){const r=le(e);Ae(r,"iterate",fn);const s=r[t](...n);return(s===-1||s===!1)&&$r(n[0])?(n[0]=le(n[0]),r[t](...n)):s}function qt(e,t,n=[]){Ct(),Ar();const r=le(e)[t].apply(e,n);return Mr(),Et(),r}const Lo=go("__proto__,__v_isRef,__isVue"),ei=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(mn));function Do(e){mn(e)||(e=String(e));const t=le(this);return Ae(t,"has",e),t.hasOwnProperty(e)}class ti{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(s?i?zo:ii:i?si:ri).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const o=xt(t);if(!s){let a;if(o&&(a=Oo[n]))return a;if(n==="hasOwnProperty")return Do}const c=Reflect.get(t,n,Me(t)?t:r);return(mn(n)?ei.has(n):Lo(n))||(s||Ae(t,"get",n),i)?c:Me(c)?o&&Pr(n)?c:c.value:_n(c)?s?li(c):Bn(c):c}}class ni extends ti{constructor(t=!1){super(!1,t)}set(t,n,r,s){let i=t[n];if(!this._isShallow){const a=Lt(i);if(!Ye(r)&&!Lt(r)&&(i=le(i),r=le(r)),!xt(t)&&Me(i)&&!Me(r))return a?!1:(i.value=r,!0)}const o=xt(t)&&Pr(n)?Number(n)<t.length:On(t,n),c=Reflect.set(t,n,r,Me(t)?t:s);return t===le(s)&&(o?St(r,i)&&ut(t,"set",n,r):ut(t,"add",n,r)),c}deleteProperty(t,n){const r=On(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&ut(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!mn(n)||!ei.has(n))&&Ae(t,"has",n),r}ownKeys(t){return Ae(t,"iterate",xt(t)?"length":Ot),Reflect.ownKeys(t)}}class No extends ti{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const $o=new ni,ko=new No,Fo=new ni(!0);const vr=e=>e,Sn=e=>Reflect.getPrototypeOf(e);function jo(e,t,n){return function(...r){const s=this.__v_raw,i=le(s),o=Xt(i),c=e==="entries"||e===Symbol.iterator&&o,a=e==="keys"&&o,p=s[e](...r),d=n?vr:t?mr:Ie;return!t&&Ae(i,"iterate",a?gr:Ot),{next(){const{value:_,done:y}=p.next();return y?{value:_,done:y}:{value:c?[d(_[0]),d(_[1])]:d(_),done:y}},[Symbol.iterator](){return this}}}}function Cn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Bo(e,t){const n={get(s){const i=this.__v_raw,o=le(i),c=le(s);e||(St(s,c)&&Ae(o,"get",s),Ae(o,"get",c));const{has:a}=Sn(o),p=t?vr:e?mr:Ie;if(a.call(o,s))return p(i.get(s));if(a.call(o,c))return p(i.get(c));i!==o&&i.get(s)},get size(){const s=this.__v_raw;return!e&&Ae(le(s),"iterate",Ot),Reflect.get(s,"size",s)},has(s){const i=this.__v_raw,o=le(i),c=le(s);return e||(St(s,c)&&Ae(o,"has",s),Ae(o,"has",c)),s===c?i.has(s):i.has(s)||i.has(c)},forEach(s,i){const o=this,c=o.__v_raw,a=le(c),p=t?vr:e?mr:Ie;return!e&&Ae(a,"iterate",Ot),c.forEach((d,_)=>s.call(i,p(d),p(_),o))}};return _o(n,e?{add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear")}:{add(s){!t&&!Ye(s)&&!Lt(s)&&(s=le(s));const i=le(this);return Sn(i).has.call(i,s)||(i.add(s),ut(i,"add",s,s)),this},set(s,i){!t&&!Ye(i)&&!Lt(i)&&(i=le(i));const o=le(this),{has:c,get:a}=Sn(o);let p=c.call(o,s);p||(s=le(s),p=c.call(o,s));const d=a.call(o,s);return o.set(s,i),p?St(i,d)&&ut(o,"set",s,i):ut(o,"add",s,i),this},delete(s){const i=le(this),{has:o,get:c}=Sn(i);let a=o.call(i,s);a||(s=le(s),a=o.call(i,s)),c&&c.call(i,s);const p=i.delete(s);return a&&ut(i,"delete",s,void 0),p},clear(){const s=le(this),i=s.size!==0,o=s.clear();return i&&ut(s,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=jo(s,e,t)}),n}function Dr(e,t){const n=Bo(e,t);return(r,s,i)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(On(n,s)&&s in r?n:r,s,i)}const Ho={get:Dr(!1,!1)},Uo={get:Dr(!1,!0)},Wo={get:Dr(!0,!1)};const ri=new WeakMap,si=new WeakMap,ii=new WeakMap,zo=new WeakMap;function Go(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Vo(e){return e.__v_skip||!Object.isExtensible(e)?0:Go(Co(e))}function Bn(e){return Lt(e)?e:Nr(e,!1,$o,Ho,ri)}function oi(e){return Nr(e,!1,Fo,Uo,si)}function li(e){return Nr(e,!0,ko,Wo,ii)}function Nr(e,t,n,r,s){if(!_n(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=s.get(e);if(i)return i;const o=Vo(e);if(o===0)return e;const c=new Proxy(e,o===2?r:n);return s.set(e,c),c}function tn(e){return Lt(e)?tn(e.__v_raw):!!(e&&e.__v_isReactive)}function Lt(e){return!!(e&&e.__v_isReadonly)}function Ye(e){return!!(e&&e.__v_isShallow)}function $r(e){return e?!!e.__v_raw:!1}function le(e){const t=e&&e.__v_raw;return t?le(t):e}function ci(e){return!On(e,"__v_skip")&&Object.isExtensible(e)&&Ro(e,"__v_skip",!0),e}const Ie=e=>_n(e)?Bn(e):e,mr=e=>_n(e)?li(e):e;function Me(e){return e?e.__v_isRef===!0:!1}function It(e){return ai(e,!1)}function Ko(e){return ai(e,!0)}function ai(e,t){return Me(e)?e:new qo(e,t)}class qo{constructor(t,n){this.dep=new Ir,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:le(t),this._value=n?t:Ie(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Ye(t)||Lt(t);t=r?t:le(t),St(t,n)&&(this._rawValue=t,this._value=r?t:Ie(t),this.dep.trigger())}}function Ht(e){return Me(e)?e.value:e}const Jo={get:(e,t,n)=>t==="__v_raw"?e:Ht(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Me(s)&&!Me(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function fi(e){return tn(e)?e:new Proxy(e,Jo)}class Qo{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ir(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=an-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ge!==this)return qs(this,!0),!0}get value(){const t=this.dep.track();return Ys(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Yo(e,t,n=!1){let r,s;return hr(e)?r=e:(r=e.get,s=e.set),new Qo(r,s,n)}const En={},In=new WeakMap;let Mt;function Xo(e,t=!1,n=Mt){if(n){let r=In.get(n);r||In.set(n,r=[]),r.push(e)}}function Zo(e,t,n=vo){const{immediate:r,deep:s,once:i,scheduler:o,augmentJob:c,call:a}=n,p=D=>s?D:Ye(D)||s===!1||s===0?wt(D,1):wt(D);let d,_,y,b,L=!1,I=!1;if(Me(e)?(_=()=>e.value,L=Ye(e)):tn(e)?(_=()=>p(e),L=!0):xt(e)?(I=!0,L=e.some(D=>tn(D)||Ye(D)),_=()=>e.map(D=>{if(Me(D))return D.value;if(tn(D))return p(D);if(hr(D))return a?a(D,2):D()})):hr(e)?t?_=a?()=>a(e,2):e:_=()=>{if(y){Ct();try{y()}finally{Et()}}const D=Mt;Mt=d;try{return a?a(e,3,[b]):e(b)}finally{Mt=D}}:_=mo,t&&s){const D=_,Q=s===!0?1/0:s;_=()=>wt(D(),Q)}const B=Po(),k=()=>{d.stop(),B&&B.active&&yo(B.effects,d)};if(i&&t){const D=t;t=(...Q)=>{D(...Q),k()}}let j=I?new Array(e.length).fill(En):En;const W=D=>{if(!(!(d.flags&1)||!d.dirty&&!D))if(t){const Q=d.run();if(s||L||(I?Q.some((ye,he)=>St(ye,j[he])):St(Q,j))){y&&y();const ye=Mt;Mt=d;try{const he=[Q,j===En?void 0:I&&j[0]===En?[]:j,b];a?a(t,3,he):t(...he),j=Q}finally{Mt=ye}}}else d.run()};return c&&c(W),d=new Vs(_),d.scheduler=o?()=>o(W,!1):W,b=D=>Xo(D,!1,d),y=d.onStop=()=>{const D=In.get(d);if(D){if(a)a(D,4);else for(const Q of D)Q();In.delete(d)}},t?r?W(!0):j=d.run():o?o(W.bind(null,!0),!0):d.run(),k.pause=d.pause.bind(d),k.resume=d.resume.bind(d),k.stop=k,k}function wt(e,t=1/0,n){if(t<=0||!_n(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Me(e))wt(e.value,t,n);else if(xt(e))for(let r=0;r<e.length;r++)wt(e[r],t,n);else if(wo(e)||Xt(e))e.forEach(r=>{wt(r,t,n)});else if(Eo(e)){for(const r in e)wt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&wt(e[r],t,n)}return e}/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function el(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const me={},Ut=[],ht=()=>{},tl=()=>!1,kr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ui=e=>e.startsWith("onUpdate:"),je=Object.assign,hi=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},nl=Object.prototype.hasOwnProperty,ve=(e,t)=>nl.call(e,t),te=Array.isArray,rl=e=>Fr(e)==="[object Map]",sl=e=>Fr(e)==="[object Set]",Z=e=>typeof e=="function",Ve=e=>typeof e=="string",di=e=>typeof e=="symbol",Te=e=>e!==null&&typeof e=="object",pi=e=>(Te(e)||Z(e))&&Z(e.then)&&Z(e.catch),gi=Object.prototype.toString,Fr=e=>gi.call(e),il=e=>Fr(e)==="[object Object]",nn=el(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Hn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ol=/-(\w)/g,Dt=Hn(e=>e.replace(ol,(t,n)=>n?n.toUpperCase():"")),ll=/\B([A-Z])/g,yn=Hn(e=>e.replace(ll,"-$1").toLowerCase()),cl=Hn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Zn=Hn(e=>e?`on${cl(e)}`:""),er=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},al=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},fl=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ts;const Un=()=>ts||(ts=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function jr(e){if(te(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=Ve(r)?pl(r):jr(r);if(s)for(const i in s)t[i]=s[i]}return t}else if(Ve(e)||Te(e))return e}const ul=/;(?![^(]*\))/g,hl=/:([^]+)/,dl=/\/\*[^]*?\*\//g;function pl(e){const t={};return e.replace(dl,"").split(ul).forEach(n=>{if(n){const r=n.split(hl);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Wn(e){let t="";if(Ve(e))t=e;else if(te(e))for(let n=0;n<e.length;n++){const r=Wn(e[n]);r&&(t+=r+" ")}else if(Te(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const vi=e=>!!(e&&e.__v_isRef===!0),_r=e=>Ve(e)?e:e==null?"":te(e)||Te(e)&&(e.toString===gi||!Z(e.toString))?vi(e)?_r(e.value):JSON.stringify(e,mi,2):String(e),mi=(e,t)=>vi(t)?mi(e,t.value):rl(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],i)=>(n[tr(r,i)+" =>"]=s,n),{})}:sl(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>tr(n))}:di(t)?tr(t):Te(t)&&!te(t)&&!il(t)?String(t):t,tr=(e,t="")=>{var n;return di(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function bn(e,t,n,r){try{return r?e(...r):e()}catch(s){zn(s,t,n)}}function it(e,t,n,r){if(Z(e)){const s=bn(e,t,n,r);return s&&pi(s)&&s.catch(i=>{zn(i,t,n)}),s}if(te(e)){const s=[];for(let i=0;i<e.length;i++)s.push(it(e[i],t,n,r));return s}}function zn(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||me;if(t){let c=t.parent;const a=t.proxy,p=`https://vuejs.org/error-reference/#runtime-${n}`;for(;c;){const d=c.ec;if(d){for(let _=0;_<d.length;_++)if(d[_](e,a,p)===!1)return}c=c.parent}if(i){Ct(),bn(i,null,10,[e,a,p]),Et();return}}gl(e,n,s,r,o)}function gl(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const Le=[];let nt=-1;const Wt=[];let _t=null,jt=0;const _i=Promise.resolve();let Ln=null;function yi(e){const t=Ln||_i;return e?t.then(this?e.bind(this):e):t}function vl(e){let t=nt+1,n=Le.length;for(;t<n;){const r=t+n>>>1,s=Le[r],i=un(s);i<e||i===e&&s.flags&2?t=r+1:n=r}return t}function Br(e){if(!(e.flags&1)){const t=un(e),n=Le[Le.length-1];!n||!(e.flags&2)&&t>=un(n)?Le.push(e):Le.splice(vl(t),0,e),e.flags|=1,bi()}}function bi(){Ln||(Ln=_i.then(xi))}function ml(e){te(e)?Wt.push(...e):_t&&e.id===-1?_t.splice(jt+1,0,e):e.flags&1||(Wt.push(e),e.flags|=1),bi()}function ns(e,t,n=nt+1){for(;n<Le.length;n++){const r=Le[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Le.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function wi(e){if(Wt.length){const t=[...new Set(Wt)].sort((n,r)=>un(n)-un(r));if(Wt.length=0,_t){_t.push(...t);return}for(_t=t,jt=0;jt<_t.length;jt++){const n=_t[jt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}_t=null,jt=0}}const un=e=>e.id==null?e.flags&2?-1:1/0:e.id;function xi(e){try{for(nt=0;nt<Le.length;nt++){const t=Le[nt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),bn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;nt<Le.length;nt++){const t=Le[nt];t&&(t.flags&=-2)}nt=-1,Le.length=0,wi(),Ln=null,(Le.length||Wt.length)&&xi()}}let st=null,Si=null;function Dn(e){const t=st;return st=e,Si=e&&e.type.__scopeId||null,t}function _l(e,t=st,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&us(-1);const i=Dn(t);let o;try{o=e(...s)}finally{Dn(i),r._d&&us(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function Pt(e,t,n,r){const s=e.dirs,i=t&&t.dirs;for(let o=0;o<s.length;o++){const c=s[o];i&&(c.oldValue=i[o].value);let a=c.dir[r];a&&(Ct(),it(a,n,8,[e.el,c,e,t]),Et())}}const yl=Symbol("_vte"),bl=e=>e.__isTeleport;function Hr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Hr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Gn(e,t){return Z(e)?je({name:e.name},t,{setup:e}):e}function Ci(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Nn(e,t,n,r,s=!1){if(te(e)){e.forEach((L,I)=>Nn(L,t&&(te(t)?t[I]:t),n,r,s));return}if(rn(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&Nn(e,t,n,r.component.subTree);return}const i=r.shapeFlag&4?Gr(r.component):r.el,o=s?null:i,{i:c,r:a}=e,p=t&&t.r,d=c.refs===me?c.refs={}:c.refs,_=c.setupState,y=le(_),b=_===me?()=>!1:L=>ve(y,L);if(p!=null&&p!==a&&(Ve(p)?(d[p]=null,b(p)&&(_[p]=null)):Me(p)&&(p.value=null)),Z(a))bn(a,c,12,[o,d]);else{const L=Ve(a),I=Me(a);if(L||I){const B=()=>{if(e.f){const k=L?b(a)?_[a]:d[a]:a.value;s?te(k)&&hi(k,i):te(k)?k.includes(i)||k.push(i):L?(d[a]=[i],b(a)&&(_[a]=d[a])):(a.value=[i],e.k&&(d[e.k]=a.value))}else L?(d[a]=o,b(a)&&(_[a]=o)):I&&(a.value=o,e.k&&(d[e.k]=o))};o?(B.id=-1,Ue(B,n)):B()}}}Un().requestIdleCallback;Un().cancelIdleCallback;const rn=e=>!!e.type.__asyncLoader,Ei=e=>e.type.__isKeepAlive;function wl(e,t){Ri(e,"a",t)}function xl(e,t){Ri(e,"da",t)}function Ri(e,t,n=De){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Vn(t,r,n),n){let s=n.parent;for(;s&&s.parent;)Ei(s.parent.vnode)&&Sl(r,t,n,s),s=s.parent}}function Sl(e,t,n,r){const s=Vn(t,e,r,!0);Ur(()=>{hi(r[t],s)},n)}function Vn(e,t,n=De,r=!1){if(n){const s=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ct();const c=wn(n),a=it(t,n,e,o);return c(),Et(),a});return r?s.unshift(i):s.push(i),i}}const pt=e=>(t,n=De)=>{(!dn||e==="sp")&&Vn(e,(...r)=>t(...r),n)},Cl=pt("bm"),El=pt("m"),Rl=pt("bu"),Tl=pt("u"),Pl=pt("bum"),Ur=pt("um"),Al=pt("sp"),Ml=pt("rtg"),Ol=pt("rtc");function Il(e,t=De){Vn("ec",e,t)}const Ll=Symbol.for("v-ndc"),yr=e=>e?Ki(e)?Gr(e):yr(e.parent):null,sn=je(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>yr(e.parent),$root:e=>yr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Pi(e),$forceUpdate:e=>e.f||(e.f=()=>{Br(e.update)}),$nextTick:e=>e.n||(e.n=yi.bind(e.proxy)),$watch:e=>ec.bind(e)}),nr=(e,t)=>e!==me&&!e.__isScriptSetup&&ve(e,t),Dl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:i,accessCache:o,type:c,appContext:a}=e;let p;if(t[0]!=="$"){const b=o[t];if(b!==void 0)switch(b){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return i[t]}else{if(nr(r,t))return o[t]=1,r[t];if(s!==me&&ve(s,t))return o[t]=2,s[t];if((p=e.propsOptions[0])&&ve(p,t))return o[t]=3,i[t];if(n!==me&&ve(n,t))return o[t]=4,n[t];br&&(o[t]=0)}}const d=sn[t];let _,y;if(d)return t==="$attrs"&&Ae(e.attrs,"get",""),d(e);if((_=c.__cssModules)&&(_=_[t]))return _;if(n!==me&&ve(n,t))return o[t]=4,n[t];if(y=a.config.globalProperties,ve(y,t))return y[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:i}=e;return nr(s,t)?(s[t]=n,!0):r!==me&&ve(r,t)?(r[t]=n,!0):ve(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:i}},o){let c;return!!n[o]||e!==me&&ve(e,o)||nr(t,o)||(c=i[0])&&ve(c,o)||ve(r,o)||ve(sn,o)||ve(s.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ve(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function rs(e){return te(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let br=!0;function Nl(e){const t=Pi(e),n=e.proxy,r=e.ctx;br=!1,t.beforeCreate&&ss(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:o,watch:c,provide:a,inject:p,created:d,beforeMount:_,mounted:y,beforeUpdate:b,updated:L,activated:I,deactivated:B,beforeDestroy:k,beforeUnmount:j,destroyed:W,unmounted:D,render:Q,renderTracked:ye,renderTriggered:he,errorCaptured:Be,serverPrefetch:Ke,expose:He,inheritAttrs:Ne,components:Ze,directives:$e,filters:X}=t;if(p&&$l(p,r,null),o)for(const se in o){const ne=o[se];Z(ne)&&(r[se]=ne.bind(n))}if(s){const se=s.call(n,n);Te(se)&&(e.data=Bn(se))}if(br=!0,i)for(const se in i){const ne=i[se],we=Z(ne)?ne.bind(n,n):Z(ne.get)?ne.get.bind(n,n):ht,ke=!Z(ne)&&Z(ne.set)?ne.set.bind(n):ht,Ce=ze({get:we,set:ke});Object.defineProperty(r,se,{enumerable:!0,configurable:!0,get:()=>Ce.value,set:xe=>Ce.value=xe})}if(c)for(const se in c)Ti(c[se],r,n,se);if(a){const se=Z(a)?a.call(n):a;Reflect.ownKeys(se).forEach(ne=>{Tn(ne,se[ne])})}d&&ss(d,e,"c");function Se(se,ne){te(ne)?ne.forEach(we=>se(we.bind(n))):ne&&se(ne.bind(n))}if(Se(Cl,_),Se(El,y),Se(Rl,b),Se(Tl,L),Se(wl,I),Se(xl,B),Se(Il,Be),Se(Ol,ye),Se(Ml,he),Se(Pl,j),Se(Ur,D),Se(Al,Ke),te(He))if(He.length){const se=e.exposed||(e.exposed={});He.forEach(ne=>{Object.defineProperty(se,ne,{get:()=>n[ne],set:we=>n[ne]=we})})}else e.exposed||(e.exposed={});Q&&e.render===ht&&(e.render=Q),Ne!=null&&(e.inheritAttrs=Ne),Ze&&(e.components=Ze),$e&&(e.directives=$e),Ke&&Ci(e)}function $l(e,t,n=ht){te(e)&&(e=wr(e));for(const r in e){const s=e[r];let i;Te(s)?"default"in s?i=dt(s.from||r,s.default,!0):i=dt(s.from||r):i=dt(s),Me(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[r]=i}}function ss(e,t,n){it(te(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ti(e,t,n,r){let s=r.includes(".")?Ui(n,r):()=>n[r];if(Ve(e)){const i=t[e];Z(i)&&Pn(s,i)}else if(Z(e))Pn(s,e.bind(n));else if(Te(e))if(te(e))e.forEach(i=>Ti(i,t,n,r));else{const i=Z(e.handler)?e.handler.bind(n):t[e.handler];Z(i)&&Pn(s,i,e)}}function Pi(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,c=i.get(t);let a;return c?a=c:!s.length&&!n&&!r?a=t:(a={},s.length&&s.forEach(p=>$n(a,p,o,!0)),$n(a,t,o)),Te(t)&&i.set(t,a),a}function $n(e,t,n,r=!1){const{mixins:s,extends:i}=t;i&&$n(e,i,n,!0),s&&s.forEach(o=>$n(e,o,n,!0));for(const o in t)if(!(r&&o==="expose")){const c=kl[o]||n&&n[o];e[o]=c?c(e[o],t[o]):t[o]}return e}const kl={data:is,props:os,emits:os,methods:Yt,computed:Yt,beforeCreate:Oe,created:Oe,beforeMount:Oe,mounted:Oe,beforeUpdate:Oe,updated:Oe,beforeDestroy:Oe,beforeUnmount:Oe,destroyed:Oe,unmounted:Oe,activated:Oe,deactivated:Oe,errorCaptured:Oe,serverPrefetch:Oe,components:Yt,directives:Yt,watch:jl,provide:is,inject:Fl};function is(e,t){return t?e?function(){return je(Z(e)?e.call(this,this):e,Z(t)?t.call(this,this):t)}:t:e}function Fl(e,t){return Yt(wr(e),wr(t))}function wr(e){if(te(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Oe(e,t){return e?[...new Set([].concat(e,t))]:t}function Yt(e,t){return e?je(Object.create(null),e,t):t}function os(e,t){return e?te(e)&&te(t)?[...new Set([...e,...t])]:je(Object.create(null),rs(e),rs(t??{})):t}function jl(e,t){if(!e)return t;if(!t)return e;const n=je(Object.create(null),e);for(const r in t)n[r]=Oe(e[r],t[r]);return n}function Ai(){return{app:null,config:{isNativeTag:tl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Bl=0;function Hl(e,t){return function(r,s=null){Z(r)||(r=je({},r)),s!=null&&!Te(s)&&(s=null);const i=Ai(),o=new WeakSet,c=[];let a=!1;const p=i.app={_uid:Bl++,_component:r,_props:s,_container:null,_context:i,_instance:null,version:xc,get config(){return i.config},set config(d){},use(d,..._){return o.has(d)||(d&&Z(d.install)?(o.add(d),d.install(p,..._)):Z(d)&&(o.add(d),d(p,..._))),p},mixin(d){return i.mixins.includes(d)||i.mixins.push(d),p},component(d,_){return _?(i.components[d]=_,p):i.components[d]},directive(d,_){return _?(i.directives[d]=_,p):i.directives[d]},mount(d,_,y){if(!a){const b=p._ceVNode||Fe(r,s);return b.appContext=i,y===!0?y="svg":y===!1&&(y=void 0),e(b,d,y),a=!0,p._container=d,d.__vue_app__=p,Gr(b.component)}},onUnmount(d){c.push(d)},unmount(){a&&(it(c,p._instance,16),e(null,p._container),delete p._container.__vue_app__)},provide(d,_){return i.provides[d]=_,p},runWithContext(d){const _=zt;zt=p;try{return d()}finally{zt=_}}};return p}}let zt=null;function Tn(e,t){if(De){let n=De.provides;const r=De.parent&&De.parent.provides;r===n&&(n=De.provides=Object.create(r)),n[e]=t}}function dt(e,t,n=!1){const r=De||st;if(r||zt){const s=zt?zt._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&Z(t)?t.call(r&&r.proxy):t}}const Mi={},Oi=()=>Object.create(Mi),Ii=e=>Object.getPrototypeOf(e)===Mi;function Ul(e,t,n,r=!1){const s={},i=Oi();e.propsDefaults=Object.create(null),Li(e,t,s,i);for(const o in e.propsOptions[0])o in s||(s[o]=void 0);n?e.props=r?s:oi(s):e.type.props?e.props=s:e.props=i,e.attrs=i}function Wl(e,t,n,r){const{props:s,attrs:i,vnode:{patchFlag:o}}=e,c=le(s),[a]=e.propsOptions;let p=!1;if((r||o>0)&&!(o&16)){if(o&8){const d=e.vnode.dynamicProps;for(let _=0;_<d.length;_++){let y=d[_];if(Kn(e.emitsOptions,y))continue;const b=t[y];if(a)if(ve(i,y))b!==i[y]&&(i[y]=b,p=!0);else{const L=Dt(y);s[L]=xr(a,c,L,b,e,!1)}else b!==i[y]&&(i[y]=b,p=!0)}}}else{Li(e,t,s,i)&&(p=!0);let d;for(const _ in c)(!t||!ve(t,_)&&((d=yn(_))===_||!ve(t,d)))&&(a?n&&(n[_]!==void 0||n[d]!==void 0)&&(s[_]=xr(a,c,_,void 0,e,!0)):delete s[_]);if(i!==c)for(const _ in i)(!t||!ve(t,_))&&(delete i[_],p=!0)}p&&ut(e.attrs,"set","")}function Li(e,t,n,r){const[s,i]=e.propsOptions;let o=!1,c;if(t)for(let a in t){if(nn(a))continue;const p=t[a];let d;s&&ve(s,d=Dt(a))?!i||!i.includes(d)?n[d]=p:(c||(c={}))[d]=p:Kn(e.emitsOptions,a)||(!(a in r)||p!==r[a])&&(r[a]=p,o=!0)}if(i){const a=le(n),p=c||me;for(let d=0;d<i.length;d++){const _=i[d];n[_]=xr(s,a,_,p[_],e,!ve(p,_))}}return o}function xr(e,t,n,r,s,i){const o=e[n];if(o!=null){const c=ve(o,"default");if(c&&r===void 0){const a=o.default;if(o.type!==Function&&!o.skipFactory&&Z(a)){const{propsDefaults:p}=s;if(n in p)r=p[n];else{const d=wn(s);r=p[n]=a.call(null,t),d()}}else r=a;s.ce&&s.ce._setProp(n,r)}o[0]&&(i&&!c?r=!1:o[1]&&(r===""||r===yn(n))&&(r=!0))}return r}const zl=new WeakMap;function Di(e,t,n=!1){const r=n?zl:t.propsCache,s=r.get(e);if(s)return s;const i=e.props,o={},c=[];let a=!1;if(!Z(e)){const d=_=>{a=!0;const[y,b]=Di(_,t,!0);je(o,y),b&&c.push(...b)};!n&&t.mixins.length&&t.mixins.forEach(d),e.extends&&d(e.extends),e.mixins&&e.mixins.forEach(d)}if(!i&&!a)return Te(e)&&r.set(e,Ut),Ut;if(te(i))for(let d=0;d<i.length;d++){const _=Dt(i[d]);ls(_)&&(o[_]=me)}else if(i)for(const d in i){const _=Dt(d);if(ls(_)){const y=i[d],b=o[_]=te(y)||Z(y)?{type:y}:je({},y),L=b.type;let I=!1,B=!0;if(te(L))for(let k=0;k<L.length;++k){const j=L[k],W=Z(j)&&j.name;if(W==="Boolean"){I=!0;break}else W==="String"&&(B=!1)}else I=Z(L)&&L.name==="Boolean";b[0]=I,b[1]=B,(I||ve(b,"default"))&&c.push(_)}}const p=[o,c];return Te(e)&&r.set(e,p),p}function ls(e){return e[0]!=="$"&&!nn(e)}const Ni=e=>e[0]==="_"||e==="$stable",Wr=e=>te(e)?e.map(rt):[rt(e)],Gl=(e,t,n)=>{if(t._n)return t;const r=_l((...s)=>Wr(t(...s)),n);return r._c=!1,r},$i=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Ni(s))continue;const i=e[s];if(Z(i))t[s]=Gl(s,i,r);else if(i!=null){const o=Wr(i);t[s]=()=>o}}},ki=(e,t)=>{const n=Wr(t);e.slots.default=()=>n},Fi=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Vl=(e,t,n)=>{const r=e.slots=Oi();if(e.vnode.shapeFlag&32){const s=t._;s?(Fi(r,t,n),n&&al(r,"_",s,!0)):$i(t,r)}else t&&ki(e,t)},Kl=(e,t,n)=>{const{vnode:r,slots:s}=e;let i=!0,o=me;if(r.shapeFlag&32){const c=t._;c?n&&c===1?i=!1:Fi(s,t,n):(i=!t.$stable,$i(t,s)),o=t}else t&&(ki(e,t),o={default:1});if(i)for(const c in s)!Ni(c)&&o[c]==null&&delete s[c]},Ue=lc;function ql(e){return Jl(e)}function Jl(e,t){const n=Un();n.__VUE__=!0;const{insert:r,remove:s,patchProp:i,createElement:o,createText:c,createComment:a,setText:p,setElementText:d,parentNode:_,nextSibling:y,setScopeId:b=ht,insertStaticContent:L}=e,I=(l,h,m,w=null,S=null,E=null,P=void 0,A=null,C=!!h.dynamicChildren)=>{if(l===h)return;l&&!Jt(l,h)&&(w=u(l),xe(l,S,E,!0),l=null),h.patchFlag===-2&&(C=!1,h.dynamicChildren=null);const{type:R,ref:F,shapeFlag:M}=h;switch(R){case qn:B(l,h,m,w);break;case Nt:k(l,h,m,w);break;case sr:l==null&&j(h,m,w,P);break;case ft:Ze(l,h,m,w,S,E,P,A,C);break;default:M&1?Q(l,h,m,w,S,E,P,A,C):M&6?$e(l,h,m,w,S,E,P,A,C):(M&64||M&128)&&R.process(l,h,m,w,S,E,P,A,C,x)}F!=null&&S&&Nn(F,l&&l.ref,E,h||l,!h)},B=(l,h,m,w)=>{if(l==null)r(h.el=c(h.children),m,w);else{const S=h.el=l.el;h.children!==l.children&&p(S,h.children)}},k=(l,h,m,w)=>{l==null?r(h.el=a(h.children||""),m,w):h.el=l.el},j=(l,h,m,w)=>{[l.el,l.anchor]=L(l.children,h,m,w,l.el,l.anchor)},W=({el:l,anchor:h},m,w)=>{let S;for(;l&&l!==h;)S=y(l),r(l,m,w),l=S;r(h,m,w)},D=({el:l,anchor:h})=>{let m;for(;l&&l!==h;)m=y(l),s(l),l=m;s(h)},Q=(l,h,m,w,S,E,P,A,C)=>{h.type==="svg"?P="svg":h.type==="math"&&(P="mathml"),l==null?ye(h,m,w,S,E,P,A,C):Ke(l,h,S,E,P,A,C)},ye=(l,h,m,w,S,E,P,A)=>{let C,R;const{props:F,shapeFlag:M,transition:O,dirs:N}=l;if(C=l.el=o(l.type,E,F&&F.is,F),M&8?d(C,l.children):M&16&&Be(l.children,C,null,w,S,rr(l,E),P,A),N&&Pt(l,null,w,"created"),he(C,l,l.scopeId,P,w),F){for(const V in F)V!=="value"&&!nn(V)&&i(C,V,null,F[V],E,w);"value"in F&&i(C,"value",null,F.value,E),(R=F.onVnodeBeforeMount)&&et(R,w,l)}N&&Pt(l,null,w,"beforeMount");const U=Ql(S,O);U&&O.beforeEnter(C),r(C,h,m),((R=F&&F.onVnodeMounted)||U||N)&&Ue(()=>{R&&et(R,w,l),U&&O.enter(C),N&&Pt(l,null,w,"mounted")},S)},he=(l,h,m,w,S)=>{if(m&&b(l,m),w)for(let E=0;E<w.length;E++)b(l,w[E]);if(S){let E=S.subTree;if(h===E||zi(E.type)&&(E.ssContent===h||E.ssFallback===h)){const P=S.vnode;he(l,P,P.scopeId,P.slotScopeIds,S.parent)}}},Be=(l,h,m,w,S,E,P,A,C=0)=>{for(let R=C;R<l.length;R++){const F=l[R]=A?yt(l[R]):rt(l[R]);I(null,F,h,m,w,S,E,P,A)}},Ke=(l,h,m,w,S,E,P)=>{const A=h.el=l.el;let{patchFlag:C,dynamicChildren:R,dirs:F}=h;C|=l.patchFlag&16;const M=l.props||me,O=h.props||me;let N;if(m&&At(m,!1),(N=O.onVnodeBeforeUpdate)&&et(N,m,h,l),F&&Pt(h,l,m,"beforeUpdate"),m&&At(m,!0),(M.innerHTML&&O.innerHTML==null||M.textContent&&O.textContent==null)&&d(A,""),R?He(l.dynamicChildren,R,A,m,w,rr(h,S),E):P||ne(l,h,A,null,m,w,rr(h,S),E,!1),C>0){if(C&16)Ne(A,M,O,m,S);else if(C&2&&M.class!==O.class&&i(A,"class",null,O.class,S),C&4&&i(A,"style",M.style,O.style,S),C&8){const U=h.dynamicProps;for(let V=0;V<U.length;V++){const G=U[V],Y=M[G],z=O[G];(z!==Y||G==="value")&&i(A,G,Y,z,S,m)}}C&1&&l.children!==h.children&&d(A,h.children)}else!P&&R==null&&Ne(A,M,O,m,S);((N=O.onVnodeUpdated)||F)&&Ue(()=>{N&&et(N,m,h,l),F&&Pt(h,l,m,"updated")},w)},He=(l,h,m,w,S,E,P)=>{for(let A=0;A<h.length;A++){const C=l[A],R=h[A],F=C.el&&(C.type===ft||!Jt(C,R)||C.shapeFlag&70)?_(C.el):m;I(C,R,F,null,w,S,E,P,!0)}},Ne=(l,h,m,w,S)=>{if(h!==m){if(h!==me)for(const E in h)!nn(E)&&!(E in m)&&i(l,E,h[E],null,S,w);for(const E in m){if(nn(E))continue;const P=m[E],A=h[E];P!==A&&E!=="value"&&i(l,E,A,P,S,w)}"value"in m&&i(l,"value",h.value,m.value,S)}},Ze=(l,h,m,w,S,E,P,A,C)=>{const R=h.el=l?l.el:c(""),F=h.anchor=l?l.anchor:c("");let{patchFlag:M,dynamicChildren:O,slotScopeIds:N}=h;N&&(A=A?A.concat(N):N),l==null?(r(R,m,w),r(F,m,w),Be(h.children||[],m,F,S,E,P,A,C)):M>0&&M&64&&O&&l.dynamicChildren?(He(l.dynamicChildren,O,m,S,E,P,A),(h.key!=null||S&&h===S.subTree)&&ji(l,h,!0)):ne(l,h,m,F,S,E,P,A,C)},$e=(l,h,m,w,S,E,P,A,C)=>{h.slotScopeIds=A,l==null?h.shapeFlag&512?S.ctx.activate(h,m,w,P,C):X(h,m,w,S,E,P,C):ot(l,h,C)},X=(l,h,m,w,S,E,P)=>{const A=l.component=vc(l,w,S);if(Ei(l)&&(A.ctx.renderer=x),mc(A,!1,P),A.asyncDep){if(S&&S.registerDep(A,Se,P),!l.el){const C=A.subTree=Fe(Nt);k(null,C,h,m)}}else Se(A,l,h,m,S,E,P)},ot=(l,h,m)=>{const w=h.component=l.component;if(ic(l,h,m))if(w.asyncDep&&!w.asyncResolved){se(w,h,m);return}else w.next=h,w.update();else h.el=l.el,w.vnode=h},Se=(l,h,m,w,S,E,P)=>{const A=()=>{if(l.isMounted){let{next:M,bu:O,u:N,parent:U,vnode:V}=l;{const H=Bi(l);if(H){M&&(M.el=V.el,se(l,M,P)),H.asyncDep.then(()=>{l.isUnmounted||A()});return}}let G=M,Y;At(l,!1),M?(M.el=V.el,se(l,M,P)):M=V,O&&er(O),(Y=M.props&&M.props.onVnodeBeforeUpdate)&&et(Y,U,M,V),At(l,!0);const z=as(l),K=l.subTree;l.subTree=z,I(K,z,_(K.el),u(K),l,S,E),M.el=z.el,G===null&&oc(l,z.el),N&&Ue(N,S),(Y=M.props&&M.props.onVnodeUpdated)&&Ue(()=>et(Y,U,M,V),S)}else{let M;const{el:O,props:N}=h,{bm:U,m:V,parent:G,root:Y,type:z}=l,K=rn(h);At(l,!1),U&&er(U),!K&&(M=N&&N.onVnodeBeforeMount)&&et(M,G,h),At(l,!0);{Y.ce&&Y.ce._injectChildStyle(z);const H=l.subTree=as(l);I(null,H,m,w,l,S,E),h.el=H.el}if(V&&Ue(V,S),!K&&(M=N&&N.onVnodeMounted)){const H=h;Ue(()=>et(M,G,H),S)}(h.shapeFlag&256||G&&rn(G.vnode)&&G.vnode.shapeFlag&256)&&l.a&&Ue(l.a,S),l.isMounted=!0,h=m=w=null}};l.scope.on();const C=l.effect=new Vs(A);l.scope.off();const R=l.update=C.run.bind(C),F=l.job=C.runIfDirty.bind(C);F.i=l,F.id=l.uid,C.scheduler=()=>Br(F),At(l,!0),R()},se=(l,h,m)=>{h.component=l;const w=l.vnode.props;l.vnode=h,l.next=null,Wl(l,h.props,w,m),Kl(l,h.children,m),Ct(),ns(l),Et()},ne=(l,h,m,w,S,E,P,A,C=!1)=>{const R=l&&l.children,F=l?l.shapeFlag:0,M=h.children,{patchFlag:O,shapeFlag:N}=h;if(O>0){if(O&128){ke(R,M,m,w,S,E,P,A,C);return}else if(O&256){we(R,M,m,w,S,E,P,A,C);return}}N&8?(F&16&&g(R,S,E),M!==R&&d(m,M)):F&16?N&16?ke(R,M,m,w,S,E,P,A,C):g(R,S,E,!0):(F&8&&d(m,""),N&16&&Be(M,m,w,S,E,P,A,C))},we=(l,h,m,w,S,E,P,A,C)=>{l=l||Ut,h=h||Ut;const R=l.length,F=h.length,M=Math.min(R,F);let O;for(O=0;O<M;O++){const N=h[O]=C?yt(h[O]):rt(h[O]);I(l[O],N,m,null,S,E,P,A,C)}R>F?g(l,S,E,!0,!1,M):Be(h,m,w,S,E,P,A,C,M)},ke=(l,h,m,w,S,E,P,A,C)=>{let R=0;const F=h.length;let M=l.length-1,O=F-1;for(;R<=M&&R<=O;){const N=l[R],U=h[R]=C?yt(h[R]):rt(h[R]);if(Jt(N,U))I(N,U,m,null,S,E,P,A,C);else break;R++}for(;R<=M&&R<=O;){const N=l[M],U=h[O]=C?yt(h[O]):rt(h[O]);if(Jt(N,U))I(N,U,m,null,S,E,P,A,C);else break;M--,O--}if(R>M){if(R<=O){const N=O+1,U=N<F?h[N].el:w;for(;R<=O;)I(null,h[R]=C?yt(h[R]):rt(h[R]),m,U,S,E,P,A,C),R++}}else if(R>O)for(;R<=M;)xe(l[R],S,E,!0),R++;else{const N=R,U=R,V=new Map;for(R=U;R<=O;R++){const q=h[R]=C?yt(h[R]):rt(h[R]);q.key!=null&&V.set(q.key,R)}let G,Y=0;const z=O-U+1;let K=!1,H=0;const re=new Array(z);for(R=0;R<z;R++)re[R]=0;for(R=N;R<=M;R++){const q=l[R];if(Y>=z){xe(q,S,E,!0);continue}let J;if(q.key!=null)J=V.get(q.key);else for(G=U;G<=O;G++)if(re[G-U]===0&&Jt(q,h[G])){J=G;break}J===void 0?xe(q,S,E,!0):(re[J-U]=R+1,J>=H?H=J:K=!0,I(q,h[J],m,null,S,E,P,A,C),Y++)}const ie=K?Yl(re):Ut;for(G=ie.length-1,R=z-1;R>=0;R--){const q=U+R,J=h[q],ee=q+1<F?h[q+1].el:w;re[R]===0?I(null,J,m,ee,S,E,P,A,C):K&&(G<0||R!==ie[G]?Ce(J,m,ee,2):G--)}}},Ce=(l,h,m,w,S=null)=>{const{el:E,type:P,transition:A,children:C,shapeFlag:R}=l;if(R&6){Ce(l.component.subTree,h,m,w);return}if(R&128){l.suspense.move(h,m,w);return}if(R&64){P.move(l,h,m,x);return}if(P===ft){r(E,h,m);for(let M=0;M<C.length;M++)Ce(C[M],h,m,w);r(l.anchor,h,m);return}if(P===sr){W(l,h,m);return}if(w!==2&&R&1&&A)if(w===0)A.beforeEnter(E),r(E,h,m),Ue(()=>A.enter(E),S);else{const{leave:M,delayLeave:O,afterLeave:N}=A,U=()=>r(E,h,m),V=()=>{M(E,()=>{U(),N&&N()})};O?O(E,U,V):V()}else r(E,h,m)},xe=(l,h,m,w=!1,S=!1)=>{const{type:E,props:P,ref:A,children:C,dynamicChildren:R,shapeFlag:F,patchFlag:M,dirs:O,cacheIndex:N}=l;if(M===-2&&(S=!1),A!=null&&Nn(A,null,m,l,!0),N!=null&&(h.renderCache[N]=void 0),F&256){h.ctx.deactivate(l);return}const U=F&1&&O,V=!rn(l);let G;if(V&&(G=P&&P.onVnodeBeforeUnmount)&&et(G,h,l),F&6)Rt(l.component,m,w);else{if(F&128){l.suspense.unmount(m,w);return}U&&Pt(l,null,h,"beforeUnmount"),F&64?l.type.remove(l,h,m,x,w):R&&!R.hasOnce&&(E!==ft||M>0&&M&64)?g(R,h,m,!1,!0):(E===ft&&M&384||!S&&F&16)&&g(C,h,m),w&&_e(l)}(V&&(G=P&&P.onVnodeUnmounted)||U)&&Ue(()=>{G&&et(G,h,l),U&&Pt(l,null,h,"unmounted")},m)},_e=l=>{const{type:h,el:m,anchor:w,transition:S}=l;if(h===ft){$(m,w);return}if(h===sr){D(l);return}const E=()=>{s(m),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(l.shapeFlag&1&&S&&!S.persisted){const{leave:P,delayLeave:A}=S,C=()=>P(m,E);A?A(l.el,E,C):C()}else E()},$=(l,h)=>{let m;for(;l!==h;)m=y(l),s(l),l=m;s(h)},Rt=(l,h,m)=>{const{bum:w,scope:S,job:E,subTree:P,um:A,m:C,a:R}=l;cs(C),cs(R),w&&er(w),S.stop(),E&&(E.flags|=8,xe(P,l,h,m)),A&&Ue(A,h),Ue(()=>{l.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&l.asyncDep&&!l.asyncResolved&&l.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},g=(l,h,m,w=!1,S=!1,E=0)=>{for(let P=E;P<l.length;P++)xe(l[P],h,m,w,S)},u=l=>{if(l.shapeFlag&6)return u(l.component.subTree);if(l.shapeFlag&128)return l.suspense.next();const h=y(l.anchor||l.el),m=h&&h[yl];return m?y(m):h};let f=!1;const v=(l,h,m)=>{l==null?h._vnode&&xe(h._vnode,null,null,!0):I(h._vnode||null,l,h,null,null,null,m),h._vnode=l,f||(f=!0,ns(),wi(),f=!1)},x={p:I,um:xe,m:Ce,r:_e,mt:X,mc:Be,pc:ne,pbc:He,n:u,o:e};return{render:v,hydrate:void 0,createApp:Hl(v)}}function rr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function At({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ql(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ji(e,t,n=!1){const r=e.children,s=t.children;if(te(r)&&te(s))for(let i=0;i<r.length;i++){const o=r[i];let c=s[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=s[i]=yt(s[i]),c.el=o.el),!n&&c.patchFlag!==-2&&ji(o,c)),c.type===qn&&(c.el=o.el)}}function Yl(e){const t=e.slice(),n=[0];let r,s,i,o,c;const a=e.length;for(r=0;r<a;r++){const p=e[r];if(p!==0){if(s=n[n.length-1],e[s]<p){t[r]=s,n.push(r);continue}for(i=0,o=n.length-1;i<o;)c=i+o>>1,e[n[c]]<p?i=c+1:o=c;p<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function Bi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Bi(t)}function cs(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Xl=Symbol.for("v-scx"),Zl=()=>dt(Xl);function Pn(e,t,n){return Hi(e,t,n)}function Hi(e,t,n=me){const{immediate:r,deep:s,flush:i,once:o}=n,c=je({},n),a=t&&r||!t&&i!=="post";let p;if(dn){if(i==="sync"){const b=Zl();p=b.__watcherHandles||(b.__watcherHandles=[])}else if(!a){const b=()=>{};return b.stop=ht,b.resume=ht,b.pause=ht,b}}const d=De;c.call=(b,L,I)=>it(b,d,L,I);let _=!1;i==="post"?c.scheduler=b=>{Ue(b,d&&d.suspense)}:i!=="sync"&&(_=!0,c.scheduler=(b,L)=>{L?b():Br(b)}),c.augmentJob=b=>{t&&(b.flags|=4),_&&(b.flags|=2,d&&(b.id=d.uid,b.i=d))};const y=Zo(e,t,c);return dn&&(p?p.push(y):a&&y()),y}function ec(e,t,n){const r=this.proxy,s=Ve(e)?e.includes(".")?Ui(r,e):()=>r[e]:e.bind(r,r);let i;Z(t)?i=t:(i=t.handler,n=t);const o=wn(this),c=Hi(s,i.bind(r),n);return o(),c}function Ui(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const tc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Dt(t)}Modifiers`]||e[`${yn(t)}Modifiers`];function nc(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||me;let s=n;const i=t.startsWith("update:"),o=i&&tc(r,t.slice(7));o&&(o.trim&&(s=n.map(d=>Ve(d)?d.trim():d)),o.number&&(s=n.map(fl)));let c,a=r[c=Zn(t)]||r[c=Zn(Dt(t))];!a&&i&&(a=r[c=Zn(yn(t))]),a&&it(a,e,6,s);const p=r[c+"Once"];if(p){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,it(p,e,6,s)}}function Wi(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const i=e.emits;let o={},c=!1;if(!Z(e)){const a=p=>{const d=Wi(p,t,!0);d&&(c=!0,je(o,d))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!c?(Te(e)&&r.set(e,null),null):(te(i)?i.forEach(a=>o[a]=null):je(o,i),Te(e)&&r.set(e,o),o)}function Kn(e,t){return!e||!kr(t)?!1:(t=t.slice(2).replace(/Once$/,""),ve(e,t[0].toLowerCase()+t.slice(1))||ve(e,yn(t))||ve(e,t))}function as(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[i],slots:o,attrs:c,emit:a,render:p,renderCache:d,props:_,data:y,setupState:b,ctx:L,inheritAttrs:I}=e,B=Dn(e);let k,j;try{if(n.shapeFlag&4){const D=s||r,Q=D;k=rt(p.call(Q,D,d,_,b,y,L)),j=c}else{const D=t;k=rt(D.length>1?D(_,{attrs:c,slots:o,emit:a}):D(_,null)),j=t.props?c:rc(c)}}catch(D){on.length=0,zn(D,e,1),k=Fe(Nt)}let W=k;if(j&&I!==!1){const D=Object.keys(j),{shapeFlag:Q}=W;D.length&&Q&7&&(i&&D.some(ui)&&(j=sc(j,i)),W=Gt(W,j,!1,!0))}return n.dirs&&(W=Gt(W,null,!1,!0),W.dirs=W.dirs?W.dirs.concat(n.dirs):n.dirs),n.transition&&Hr(W,n.transition),k=W,Dn(B),k}const rc=e=>{let t;for(const n in e)(n==="class"||n==="style"||kr(n))&&((t||(t={}))[n]=e[n]);return t},sc=(e,t)=>{const n={};for(const r in e)(!ui(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function ic(e,t,n){const{props:r,children:s,component:i}=e,{props:o,children:c,patchFlag:a}=t,p=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?fs(r,o,p):!!o;if(a&8){const d=t.dynamicProps;for(let _=0;_<d.length;_++){const y=d[_];if(o[y]!==r[y]&&!Kn(p,y))return!0}}}else return(s||c)&&(!c||!c.$stable)?!0:r===o?!1:r?o?fs(r,o,p):!0:!!o;return!1}function fs(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const i=r[s];if(t[i]!==e[i]&&!Kn(n,i))return!0}return!1}function oc({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const zi=e=>e.__isSuspense;function lc(e,t){t&&t.pendingBranch?te(e)?t.effects.push(...e):t.effects.push(e):ml(e)}const ft=Symbol.for("v-fgt"),qn=Symbol.for("v-txt"),Nt=Symbol.for("v-cmt"),sr=Symbol.for("v-stc"),on=[];let Ge=null;function qe(e=!1){on.push(Ge=e?null:[])}function cc(){on.pop(),Ge=on[on.length-1]||null}let hn=1;function us(e,t=!1){hn+=e,e<0&&Ge&&t&&(Ge.hasOnce=!0)}function Gi(e){return e.dynamicChildren=hn>0?Ge||Ut:null,cc(),hn>0&&Ge&&Ge.push(e),e}function tt(e,t,n,r,s,i){return Gi(Ee(e,t,n,r,s,i,!0))}function ac(e,t,n,r,s){return Gi(Fe(e,t,n,r,s,!0))}function kn(e){return e?e.__v_isVNode===!0:!1}function Jt(e,t){return e.type===t.type&&e.key===t.key}const Vi=({key:e})=>e??null,An=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ve(e)||Me(e)||Z(e)?{i:st,r:e,k:t,f:!!n}:e:null);function Ee(e,t=null,n=null,r=0,s=null,i=e===ft?0:1,o=!1,c=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Vi(t),ref:t&&An(t),scopeId:Si,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:st};return c?(zr(a,n),i&128&&e.normalize(a)):n&&(a.shapeFlag|=Ve(n)?8:16),hn>0&&!o&&Ge&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&Ge.push(a),a}const Fe=fc;function fc(e,t=null,n=null,r=0,s=null,i=!1){if((!e||e===Ll)&&(e=Nt),kn(e)){const c=Gt(e,t,!0);return n&&zr(c,n),hn>0&&!i&&Ge&&(c.shapeFlag&6?Ge[Ge.indexOf(e)]=c:Ge.push(c)),c.patchFlag=-2,c}if(wc(e)&&(e=e.__vccOpts),t){t=uc(t);let{class:c,style:a}=t;c&&!Ve(c)&&(t.class=Wn(c)),Te(a)&&($r(a)&&!te(a)&&(a=je({},a)),t.style=jr(a))}const o=Ve(e)?1:zi(e)?128:bl(e)?64:Te(e)?4:Z(e)?2:0;return Ee(e,t,n,r,s,o,i,!0)}function uc(e){return e?$r(e)||Ii(e)?je({},e):e:null}function Gt(e,t,n=!1,r=!1){const{props:s,ref:i,patchFlag:o,children:c,transition:a}=e,p=t?dc(s||{},t):s,d={__v_isVNode:!0,__v_skip:!0,type:e.type,props:p,key:p&&Vi(p),ref:t&&t.ref?n&&i?te(i)?i.concat(An(t)):[i,An(t)]:An(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ft?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Gt(e.ssContent),ssFallback:e.ssFallback&&Gt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&Hr(d,a.clone(d)),d}function hc(e=" ",t=0){return Fe(qn,null,e,t)}function Rn(e="",t=!1){return t?(qe(),ac(Nt,null,e)):Fe(Nt,null,e)}function rt(e){return e==null||typeof e=="boolean"?Fe(Nt):te(e)?Fe(ft,null,e.slice()):kn(e)?yt(e):Fe(qn,null,String(e))}function yt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Gt(e)}function zr(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(te(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),zr(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Ii(t)?t._ctx=st:s===3&&st&&(st.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Z(t)?(t={default:t,_ctx:st},n=32):(t=String(t),r&64?(n=16,t=[hc(t)]):n=8);e.children=t,e.shapeFlag|=n}function dc(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Wn([t.class,r.class]));else if(s==="style")t.style=jr([t.style,r.style]);else if(kr(s)){const i=t[s],o=r[s];o&&i!==o&&!(te(i)&&i.includes(o))&&(t[s]=i?[].concat(i,o):o)}else s!==""&&(t[s]=r[s])}return t}function et(e,t,n,r=null){it(e,t,7,[n,r])}const pc=Ai();let gc=0;function vc(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||pc,i={uid:gc++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Gs(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Di(r,s),emitsOptions:Wi(r,s),emit:null,emitted:null,propsDefaults:me,inheritAttrs:r.inheritAttrs,ctx:me,data:me,props:me,attrs:me,slots:me,refs:me,setupState:me,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=nc.bind(null,i),e.ce&&e.ce(i),i}let De=null,Fn,Sr;{const e=Un(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),i=>{s.length>1?s.forEach(o=>o(i)):s[0](i)}};Fn=t("__VUE_INSTANCE_SETTERS__",n=>De=n),Sr=t("__VUE_SSR_SETTERS__",n=>dn=n)}const wn=e=>{const t=De;return Fn(e),e.scope.on(),()=>{e.scope.off(),Fn(t)}},hs=()=>{De&&De.scope.off(),Fn(null)};function Ki(e){return e.vnode.shapeFlag&4}let dn=!1;function mc(e,t=!1,n=!1){t&&Sr(t);const{props:r,children:s}=e.vnode,i=Ki(e);Ul(e,r,i,t),Vl(e,s,n);const o=i?_c(e,t):void 0;return t&&Sr(!1),o}function _c(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Dl);const{setup:r}=n;if(r){Ct();const s=e.setupContext=r.length>1?bc(e):null,i=wn(e),o=bn(r,e,0,[e.props,s]),c=pi(o);if(Et(),i(),(c||e.sp)&&!rn(e)&&Ci(e),c){if(o.then(hs,hs),t)return o.then(a=>{ds(e,a)}).catch(a=>{zn(a,e,0)});e.asyncDep=o}else ds(e,o)}else qi(e)}function ds(e,t,n){Z(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Te(t)&&(e.setupState=fi(t)),qi(e)}function qi(e,t,n){const r=e.type;e.render||(e.render=r.render||ht);{const s=wn(e);Ct();try{Nl(e)}finally{Et(),s()}}}const yc={get(e,t){return Ae(e,"get",""),e[t]}};function bc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,yc),slots:e.slots,emit:e.emit,expose:t}}function Gr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fi(ci(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in sn)return sn[n](e)},has(t,n){return n in t||n in sn}})):e.proxy}function wc(e){return Z(e)&&"__vccOpts"in e}const ze=(e,t)=>Yo(e,t,dn);function Ji(e,t,n){const r=arguments.length;return r===2?Te(t)&&!te(t)?kn(t)?Fe(e,null,[t]):Fe(e,t):Fe(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&kn(n)&&(n=[n]),Fe(e,t,n))}const xc="3.5.13";/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Sc(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Cc=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ec=e=>e.startsWith("onUpdate:"),Rc=Object.assign,Qi=Array.isArray,Yi=e=>typeof e=="function",pn=e=>typeof e=="string",Tc=e=>typeof e=="symbol",Vr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pc=/-(\w)/g,Ac=Vr(e=>e.replace(Pc,(t,n)=>n?n.toUpperCase():"")),Mc=/\B([A-Z])/g,Xi=Vr(e=>e.replace(Mc,"-$1").toLowerCase()),Oc=Vr(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ic="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Lc=Sc(Ic);function Zi(e){return!!e||e===""}/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Cr;const ps=typeof window<"u"&&window.trustedTypes;if(ps)try{Cr=ps.createPolicy("vue",{createHTML:e=>e})}catch{}const eo=Cr?e=>Cr.createHTML(e):e=>e,Dc="http://www.w3.org/2000/svg",Nc="http://www.w3.org/1998/Math/MathML",at=typeof document<"u"?document:null,gs=at&&at.createElement("template"),$c={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?at.createElementNS(Dc,e):t==="mathml"?at.createElementNS(Nc,e):n?at.createElement(e,{is:n}):at.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>at.createTextNode(e),createComment:e=>at.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>at.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,i){const o=n?n.previousSibling:t.lastChild;if(s&&(s===i||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===i||!(s=s.nextSibling)););else{gs.innerHTML=eo(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const c=gs.content;if(r==="svg"||r==="mathml"){const a=c.firstChild;for(;a.firstChild;)c.appendChild(a.firstChild);c.removeChild(a)}t.insertBefore(c,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},kc=Symbol("_vtc");function Fc(e,t,n){const r=e[kc];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const vs=Symbol("_vod"),jc=Symbol("_vsh"),Bc=Symbol(""),Hc=/(^|;)\s*display\s*:/;function Uc(e,t,n){const r=e.style,s=pn(n);let i=!1;if(n&&!s){if(t)if(pn(t))for(const o of t.split(";")){const c=o.slice(0,o.indexOf(":")).trim();n[c]==null&&Mn(r,c,"")}else for(const o in t)n[o]==null&&Mn(r,o,"");for(const o in n)o==="display"&&(i=!0),Mn(r,o,n[o])}else if(s){if(t!==n){const o=r[Bc];o&&(n+=";"+o),r.cssText=n,i=Hc.test(n)}}else t&&e.removeAttribute("style");vs in e&&(e[vs]=i?r.display:"",e[jc]&&(r.display="none"))}const ms=/\s*!important$/;function Mn(e,t,n){if(Qi(n))n.forEach(r=>Mn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Wc(e,t);ms.test(n)?e.setProperty(Xi(r),n.replace(ms,""),"important"):e[r]=n}}const _s=["Webkit","Moz","ms"],ir={};function Wc(e,t){const n=ir[t];if(n)return n;let r=Dt(t);if(r!=="filter"&&r in e)return ir[t]=r;r=Oc(r);for(let s=0;s<_s.length;s++){const i=_s[s]+r;if(i in e)return ir[t]=i}return t}const ys="http://www.w3.org/1999/xlink";function bs(e,t,n,r,s,i=Lc(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ys,t.slice(6,t.length)):e.setAttributeNS(ys,t,n):n==null||i&&!Zi(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Tc(n)?String(n):n)}function ws(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?eo(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(c!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=Zi(n):n==null&&c==="string"?(n="",o=!0):c==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(s||t)}function zc(e,t,n,r){e.addEventListener(t,n,r)}function Gc(e,t,n,r){e.removeEventListener(t,n,r)}const xs=Symbol("_vei");function Vc(e,t,n,r,s=null){const i=e[xs]||(e[xs]={}),o=i[t];if(r&&o)o.value=r;else{const[c,a]=Kc(t);if(r){const p=i[t]=Qc(r,s);zc(e,c,p,a)}else o&&(Gc(e,c,o,a),i[t]=void 0)}}const Ss=/(?:Once|Passive|Capture)$/;function Kc(e){let t;if(Ss.test(e)){t={};let r;for(;r=e.match(Ss);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Xi(e.slice(2)),t]}let or=0;const qc=Promise.resolve(),Jc=()=>or||(qc.then(()=>or=0),or=Date.now());function Qc(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;it(Yc(r,n.value),t,5,[r])};return n.value=e,n.attached=Jc(),n}function Yc(e,t){if(Qi(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Cs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Xc=(e,t,n,r,s,i)=>{const o=s==="svg";t==="class"?Fc(e,r,o):t==="style"?Uc(e,n,r):Cc(t)?Ec(t)||Vc(e,t,n,r,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Zc(e,t,r,o))?(ws(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&bs(e,t,r,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!pn(r))?ws(e,Ac(t),r,i,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),bs(e,t,r,o))};function Zc(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Cs(t)&&Yi(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Cs(t)&&pn(n)?!1:t in e}const ea=Rc({patchProp:Xc},$c);let Es;function ta(){return Es||(Es=ql(ea))}const na=(...e)=>{const t=ta().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=sa(r);if(!s)return;const i=t._component;!Yi(i)&&!i.render&&!i.template&&(i.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const o=n(s,!1,ra(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),o},t};function ra(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function sa(e){return pn(e)?document.querySelector(e):e}/*!
 * pinia v3.0.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const ia=Symbol();var Rs;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Rs||(Rs={}));function oa(){const e=To(!0),t=e.run(()=>It({}));let n=[],r=[];const s=ci({install(i){s._a=i,i.provide(ia,s),i.config.globalProperties.$pinia=s,r.forEach(o=>n.push(o)),r=[]},use(i){return this._a?n.push(i):r.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}function la(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lr={exports:{}},Ts;function ca(){return Ts||(Ts=1,(function(e){(function(t){var n=typeof window=="object"&&!!window.document,r=n?window:Object;t(r,n),e.exports&&(e.exports=r.Recorder)})(function(t,n){var r=function(){},s=function(g){return typeof g=="number"},i=function(g){return JSON.stringify(g)},o=function(g){return new se(g)},c=o.LM="2025-01-11 09:28",a="https://github.com/xiangyuecn/Recorder",p="Recorder",d="getUserMedia",_="srcSampleRate",y="sampleRate",b="bitRate",L="catch",I=t[p];if(I&&I.LM==c){I.CLog(I.i18n.$T("K8zP::重复导入{1}",0,p),3);return}o.IsOpen=function(){var g=o.Stream;if(g){var u=$e(g),f=u[0];if(f){var v=f.readyState;return v=="live"||v==f.LIVE}}return!1},o.BufferSize=4096,o.Destroy=function(){X(p+" Destroy"),Ne();for(var g in B)B[g]()};var B={};o.BindDestroy=function(g,u){B[g]=u},o.Support=function(){if(!n)return!1;var g=navigator.mediaDevices||{};return g[d]||(g=navigator,g[d]||(g[d]=g.webkitGetUserMedia||g.mozGetUserMedia||g.msGetUserMedia)),!(!g[d]||(o.Scope=g,!o.GetContext()))},o.GetContext=function(g){if(!n)return null;var u=window.AudioContext;if(u||(u=window.webkitAudioContext),!u)return null;var f=o.Ctx,v=0;return f||(f=o.Ctx=new u,v=1,o.NewCtxs=o.NewCtxs||[],o.BindDestroy("Ctx",function(){var x=o.Ctx;x&&x.close&&(k(x),o.Ctx=0);var T=o.NewCtxs;o.NewCtxs=[];for(var l=0;l<T.length;l++)k(T[l])})),g&&f.close&&(v||(f._useC||k(f),f=new u),f._useC=1,o.NewCtxs.push(f)),f},o.CloseNewCtx=function(g){if(g&&g.close){k(g);for(var u=o.NewCtxs||[],f=u.length,v=0;v<u.length;v++)if(u[v]==g){u.splice(v,1);break}X($("mSxV::剩{1}个GetContext未close",0,f+"-1="+u.length),u.length?3:0)}};var k=function(g){if(g&&g.close&&!g._isC&&(g._isC=1,g.state!="closed"))try{g.close()}catch(u){X("ctx close err",1,u)}},j=o.ResumeCtx=function(g,u,f,v){var x=0,T=0,l=0,h=0,m="EventListener",w="ResumeCtx ",S=function(A,C){T&&E(),x||(x=1,A&&v(A,h),C&&f(h)),C&&(!g._LsSC&&g["add"+m]&&g["add"+m]("statechange",P),g._LsSC=1,l=1)},E=function(A){if(!(A&&T)){T=A?1:0;for(var C=["focus","mousedown","mouseup","touchstart","touchend"],R=0;R<C.length;R++)window[(A?"add":"remove")+m](C[R],P,!0)}},P=function(){var A=g.state,C=W(A);if(!x&&!u(C?++h:h))return S();C?(l&&X(w+"sc "+A,3),E(1),g.resume().then(function(){l&&X(w+"sc "+g.state),S(0,1)})[L](function(R){X(w+"error",1,R),W(g.state)||S(R.message||"error")})):A=="closed"?(l&&!g._isC&&X(w+"sc "+A,1),S("ctx closed")):S(0,1)};P()},W=o.CtxSpEnd=function(g){return g=="suspended"||g=="interrupted"},D=function(g){var u=g.state,f="ctx.state="+u;return W(u)&&(f+=$("nMIy::（注意：ctx不是running状态，rec.open和start至少要有一个在用户操作(触摸、点击等)时进行调用，否则将在rec.start时尝试进行ctx.resume，可能会产生兼容性问题(仅iOS)，请参阅文档中runningContext配置）")),f},Q="ConnectEnableWebM";o[Q]=!0;var ye="ConnectEnableWorklet";o[ye]=!1;var he=function(g){var u=g.BufferSize||o.BufferSize,f=g.Stream,v=f._c,x=v[y],T={},l=$e(f),h=l[0],m=null,w="";if(h&&h.getSettings){m=h.getSettings();var S=m[y];S&&S!=x&&(w=$("eS8i::Stream的采样率{1}不等于{2}，将进行采样率转换（注意：音质不会变好甚至可能变差），主要在移动端未禁用回声消除时会产生此现象，浏览器有回声消除时可能只会返回16k采样率的音频数据，",0,S,x))}f._ts=m,X(w+"Stream TrackSet: "+i(m),w?3:0);var E=function(q){var J=f._m=v.createMediaStreamSource(f),ee=v.destination,de="createMediaStreamDestination";v[de]&&(ee=f._d=v[de]()),J.connect(q),q.connect(ee)},P,A,C,R="",F=f._call,M=function(q,J){for(var ee in F){if(J!=x){T.index=0,T=o.SampleData([q],J,x,T,{_sum:1});var de=T.data,fe=T._sum}else{T={};for(var ce=q.length,de=new Int16Array(ce),fe=0,be=0;be<ce;be++){var ue=Math.max(-1,Math.min(1,q[be]));ue=ue<0?ue*32768:ue*32767,de[be]=ue,fe+=Math.abs(ue)}}for(var pe in F)F[pe](de,fe);return}},O="ScriptProcessor",N="audioWorklet",U=p+" "+N,V="RecProc",G="MediaRecorder",Y=G+".WebM.PCM",z=v.createScriptProcessor||v.createJavaScriptNode,K=$("ZGlf::。由于{1}内部1秒375次回调，在移动端可能会有性能问题导致回调丢失录音变短，PC端无影响，暂不建议开启{1}。",0,N),H=function(){A=f.isWorklet=!1,Ke(f),X($("7TU0::Connect采用老的{1}，",0,O)+_e.get(o[ye]?$("JwCL::但已设置{1}尝试启用{2}",2):$("VGjB::可设置{1}尝试启用{2}",2),[p+"."+ye+"=true",N])+R+K,3);var q=f._p=z.call(v,u,1,1);E(q),q.onaudioprocess=function(J){var ee=J.inputBuffer.getChannelData(0);M(ee,x)}},re=function(){P=f.isWebM=!1,He(f),A=f.isWorklet=!z||o[ye];var q=window.AudioWorkletNode;if(!(A&&v[N]&&q)){H();return}var J=function(){var be=function(pe){return pe.toString().replace(/^function|DEL_/g,"").replace(/\$RA/g,U)},ue="class "+V+" extends AudioWorkletProcessor{";return ue+="constructor "+be(function(pe){DEL_super(pe);var Pe=this,ae=pe.processorOptions.bufferSize;Pe.bufferSize=ae,Pe.buffer=new Float32Array(ae*2),Pe.pos=0,Pe.port.onmessage=function(Re){Re.data.kill&&(Pe.kill=!0,$C.log("$RA kill call"))},$C.log("$RA .ctor call",pe)}),ue+="process "+be(function(pe,Pe,ae){var Re=this,$t=Re.bufferSize,gt=Re.buffer,vt=Re.pos;if(pe=(pe[0]||[])[0]||[],pe.length){gt.set(pe,vt),vt+=pe.length;var Tt=~~(vt/$t)*$t;if(Tt){this.port.postMessage({val:gt.slice(0,Tt)});var xn=gt.subarray(Tt,vt);gt=new Float32Array($t*2),gt.set(xn),vt=xn.length,Re.buffer=gt}Re.pos=vt}return!Re.kill}),ue+='}try{registerProcessor("'+V+'", '+V+')}catch(e){$C.error("'+U+' Reg Error",e)}',ue=ue.replace(/\$C\./g,"console."),"data:text/javascript;base64,"+btoa(unescape(encodeURIComponent(ue)))},ee=function(){return A&&f._na},de=f._na=function(){C!==""&&(clearTimeout(C),C=setTimeout(function(){C=0,ee()&&(X($("MxX1::{1}未返回任何音频，恢复使用{2}",0,N,O),3),z&&H())},500))},fe=function(){if(ee()){var be=f._n=new q(v,V,{processorOptions:{bufferSize:u}});E(be),be.port.onmessage=function(ue){C&&(clearTimeout(C),C=""),ee()?M(ue.data.val,x):A||X($("XUap::{1}多余回调",0,N),3)},X($("yOta::Connect采用{1}，设置{2}可恢复老式{3}",0,N,p+"."+ye+"=false",O)+R+K,3)}},ce=function(){if(ee()){if(v[V]){fe();return}var be=J();v[N].addModule(be).then(function(ue){ee()&&(v[V]=1,fe(),C&&de())})[L](function(ue){X(N+".addModule Error",1,ue),ee()&&H()})}};j(v,function(){return ee()},ce,ce)},ie=function(){var q=window[G],J="ondataavailable",ee="audio/webm; codecs=pcm";P=f.isWebM=o[Q];var de=q&&J in q.prototype&&q.isTypeSupported(ee);if(R=de?"":$("VwPd::（此浏览器不支持{1}）",0,Y),!P||!de){re();return}var fe=function(){return P&&f._ra};f._ra=function(){C!==""&&(clearTimeout(C),C=setTimeout(function(){fe()&&(X($("vHnb::{1}未返回任何音频，降级使用{2}",0,G,N),3),re())},500))};var ce=Object.assign({mimeType:ee},o.ConnectWebMOptions),be=f._r=new q(f,ce),ue=f._rd={};be[J]=function(pe){var Pe=new FileReader;Pe.onloadend=function(){if(fe()){var ae=ne(new Uint8Array(Pe.result),ue);if(!ae)return;if(ae==-1){re();return}C&&(clearTimeout(C),C=""),M(ae,ue.webmSR)}else P||X($("O9P7::{1}多余回调",0,G),3)},Pe.readAsArrayBuffer(pe.data)};try{be.start(~~(u/48)),X($("LMEm::Connect采用{1}，设置{2}可恢复使用{3}或老式{4}",0,Y,p+"."+Q+"=false",N,O))}catch(pe){X("mr start err",1,pe),re()}};ie()},Be=function(g){g._na&&g._na(),g._ra&&g._ra()},Ke=function(g){g._na=null,g._n&&(g._n.port.postMessage({kill:!0}),g._n.disconnect(),g._n=null)},He=function(g){if(g._ra=null,g._r){try{g._r.stop()}catch(u){X("mr stop err",1,u)}g._r=null}},Ne=function(g){g=g||o;var u=g==o,f=g.Stream;f&&(f._m&&(f._m.disconnect(),f._m=null),!f._RC&&f._c&&o.CloseNewCtx(f._c),f._RC=null,f._c=null,f._d&&(Ze(f._d.stream),f._d=null),f._p&&(f._p.disconnect(),f._p.onaudioprocess=f._p=null),Ke(f),He(f),u&&Ze(f)),g.Stream=0},Ze=o.StopS_=function(g){for(var u=$e(g),f=0;f<u.length;f++){var v=u[f];v.stop&&v.stop()}g.stop&&g.stop()},$e=function(g){var u=0,f=0,v=[];g.getAudioTracks&&(u=g.getAudioTracks(),f=g.getVideoTracks()),u||(u=g.audioTracks,f=g.videoTracks);for(var x=0,T=u?u.length:0;x<T;x++)v.push(u[x]);for(var x=0,T=f?f.length:0;x<T;x++)v.push(f[x]);return v};o.SampleData=function(g,u,f,v,x){var T="SampleData";v||(v={});var l=v.index||0,h=v.offset||0,m=v.raisePrev||0,w=v.filter;if(w&&w.fn&&(w.sr&&w.sr!=u||w.srn&&w.srn!=f)&&(w=null,X($("d48C::{1}的filter采样率变了，重设滤波",0,T),3)),!w)if(f<=u){var S=f>u*3/4?0:f/2*3/4;w={fn:S?o.IIRFilter(!0,u,S):0}}else{var S=u>f*3/4?0:u/2*3/4;w={fn:S?o.IIRFilter(!0,f,S):0}}w.sr=u,w.srn=f;var E=w.fn,P=v.frameNext||[];x||(x={});var A=x.frameSize||1;x.frameType&&(A=x.frameType=="mp3"?1152:1);var C=x._sum,R=0,F=g.length;l>F+1&&X($("tlbC::{1}似乎传入了未重置chunk {2}",0,T,l+">"+F),3);for(var M=0,O=l;O<F;O++)M+=g[O].length;var N=u/f;if(N>1)M=Math.max(0,M-Math.floor(h)),M=Math.floor(M/N);else if(N<1){var U=1/N;M=Math.floor(M*U)}M+=P.length;for(var V=new Int16Array(M),G=0,O=0;O<P.length;O++)V[G]=P[O],G++;for(;l<F;l++){var Y=g[l],z=Y instanceof Float32Array,O=h,K=Y.length,H=E&&E.Embed,re=0,ie=0,q=0,J=0;if(N<1){for(var ee=G+O,de=m,fe=0;fe<K;fe++){var ce=Y[fe];z&&(ce=Math.max(-1,Math.min(1,ce)),ce=ce<0?ce*32768:ce*32767);var be=Math.floor(ee);ee+=U;for(var ue=Math.floor(ee),pe=(ce-de)/(ue-be),Pe=1;be<ue;be++,Pe++){var ae=Math.floor(de+Pe*pe);H?(q=ae,J=H.b0*q+H.b1*H.x1+H.b0*H.x2-H.a1*H.y1-H.a2*H.y2,H.x2=H.x1,H.x1=q,H.y2=H.y1,H.y1=J,ae=J):ae=E?E(ae):ae,ae>32767?ae=32767:ae<-32768&&(ae=-32768),C&&(R+=Math.abs(ae)),V[be]=ae,G++}de=m=ce,O+=U}h=O%1;continue}for(var fe=0,Re=0;fe<K;fe++,Re++){if(Re<K){var ce=Y[Re];z&&(ce=Math.max(-1,Math.min(1,ce)),ce=ce<0?ce*32768:ce*32767),H?(q=ce,J=H.b0*q+H.b1*H.x1+H.b0*H.x2-H.a1*H.y1-H.a2*H.y2,H.x2=H.x1,H.x1=q,H.y2=H.y1,H.y1=J):J=E?E(ce):ce}if(re=ie,ie=J,Re==0){fe--;continue}var $t=Math.floor(O);if(fe==$t){var gt=Math.ceil(O),vt=O-$t,Tt=re,xn=gt<K?ie:Tt,kt=Tt+(xn-Tt)*vt;kt>32767?kt=32767:kt<-32768&&(kt=-32768),C&&(R+=Math.abs(kt)),V[G]=kt,G++,O+=N}}h=Math.max(0,O-K)}N<1&&G+1==M&&(M--,V=new Int16Array(V.buffer.slice(0,M*2))),G-1!=M&&G!=M&&X(T+" idx:"+G+" != size:"+M,3),P=null;var Qr=M%A;if(Qr>0){var Yr=(M-Qr)*2;P=new Int16Array(V.buffer.slice(Yr)),V=new Int16Array(V.buffer.slice(0,Yr))}var Xr={index:l,offset:h,raisePrev:m,filter:w,frameNext:P,sampleRate:f,data:V};return C&&(Xr._sum=R),Xr},o.IIRFilter=function(g,u,f){var v=2*Math.PI*f/u,x=Math.sin(v),T=Math.cos(v),l=x/2,h=1+l,m=-2*T/h,w=(1-l)/h;if(g)var S=(1-T)/2/h,E=(1-T)/h;else var S=(1+T)/2/h,E=-(1+T)/h;var P=0,A=0,C=0,R=0,F=0,M=function(O){return C=S*O+E*P+S*A-m*R-w*F,A=P,P=O,F=R,R=C,C};return M.Embed={x1:0,x2:0,y1:0,y2:0,b0:S,b1:E,a1:m,a2:w},M},o.PowerLevel=function(g,u){var f=g/u||0,v;return f<1251?v=Math.round(f/1250*10):v=Math.round(Math.min(100,Math.max(0,(1+Math.log(f/1e4)/Math.log(10))*100))),v},o.PowerDBFS=function(g){var u=Math.max(.1,g||0),f=32767;return u=Math.min(u,f),u=20*Math.log(u/f)/Math.log(10),Math.max(-100,Math.round(u))},o.CLog=function(g,u){if(typeof console=="object"){var f=new Date,v=("0"+f.getMinutes()).substr(-2)+":"+("0"+f.getSeconds()).substr(-2)+"."+("00"+f.getMilliseconds()).substr(-3),x=this&&this.envIn&&this.envCheck&&this.id,T=["["+v+" "+p+(x?":"+x:"")+"]"+g],l=arguments,h=o.CLog,m=2,w=h.log||console.log;for(s(u)?w=u==1?h.error||console.error:u==3?h.warn||console.warn:w:m=1;m<l.length;m++)T.push(l[m]);ot?w&&w("[IsLoser]"+T[0],T.length>1?T:""):w.apply(console,T)}};var X=function(){o.CLog.apply(this,arguments)},ot=!0;try{ot=!console.log.apply}catch{}var Se=0;function se(g){var u=this;u.id=++Se,Rt();var f={type:"mp3",onProcess:r};for(var v in g)f[v]=g[v];u.set=f;var x=f[b],T=f[y];(x&&!s(x)||T&&!s(T))&&u.CLog($.G("IllegalArgs-1",[$("VtS4::{1}和{2}必须是数值",0,y,b)]),1,g),f[b]=+x||16,f[y]=+T||16e3,u.state=0,u._S=9,u.Sync={O:9,C:9}}o.Sync={O:9,C:9},o.prototype=se.prototype={CLog:X,_streamStore:function(){return this.set.sourceStream?this:o},_streamGet:function(){return this._streamStore().Stream},_streamCtx:function(){var g=this._streamGet();return g&&g._c},open:function(g,u){var f=this,v=f.set,x=f._streamStore(),T=0;g=g||r;var l=function(z,K){K=!!K,f.CLog($("5tWi::录音open失败：")+z+",isUserNotAllow:"+K,1),T&&o.CloseNewCtx(T),u&&u(z,K)};f._streamTag=d;var h=function(){f.CLog("open ok, id:"+f.id+" stream:"+f._streamTag),g(),f._SO=0},m=x.Sync,w=++m.O,S=m.C;f._O=f._O_=w,f._SO=f._S;var E=function(){if(S!=m.C||!f._O){var z=$("dFm8::open被取消");return w==m.O?f.close():z=$("VtJO::open被中断"),l(z),!0}};if(!n){l($.G("NonBrowser-1",["open"])+$("EMJq::，可尝试使用RecordApp解决方案")+"("+a+"/tree/master/app-support-sample)");return}var P=f.envCheck({envName:"H5",canProcess:!0});if(P){l($("A5bm::不能录音：")+P);return}var A,C=function(){A=v.runningContext,A||(A=T=o.GetContext(!0))};if(v.sourceStream){if(f._streamTag="set.sourceStream",!o.GetContext()){l($("1iU7::不支持此浏览器从流中获取录音"));return}C(),Ne(x);var R=f.Stream=v.sourceStream;R._c=A,R._RC=v.runningContext,R._call={};try{he(x)}catch(z){Ne(x),l($("BTW2::从流中打开录音失败：")+z.message);return}h();return}var F=function(z,K){try{window.top.a}catch{l($("Nclz::无权录音(跨域，请尝试给iframe添加麦克风访问策略，如{1})",0,'allow="camera;microphone"'));return}M(1,z)&&(/Found/i.test(z)?l(K+$("jBa9::，无可用麦克风")):l(K))},M=function(z,K){if(/Permission|Allow/i.test(K))z&&l($("gyO5::用户拒绝了录音权限"),!0);else if(window.isSecureContext===!1)z&&l($("oWNo::浏览器禁止不安全页面录音，可开启https解决"));else return 1};if(o.IsOpen()){h();return}if(!o.Support()){F("",$("COxc::此浏览器不支持录音"));return}C();var O=function(z){setTimeout(function(){z._call={};var K=o.Stream;K&&(Ne(),z._call=K._call),o.Stream=z,z._c=A,z._RC=v.runningContext,!E()&&(o.IsOpen()?(K&&f.CLog($("upb8::发现同时多次调用open"),1),he(x),h()):l($("Q1GA::录音功能无效：无音频流")))},100)},N=function(z){var K=z.name||z.message||z.code+":"+z,H="";U==1&&M(0,K)&&(H=$("KxE2::，将尝试禁用回声消除后重试"));var re=$("xEQR::请求录音权限错误"),ie=$("bDOG::无法录音：");f.CLog(re+H+"|"+z,H||G?3:1,z),H?(V=K,G=z,Y(1)):G?(f.CLog(re+"|"+G,1,G),F(V,ie+G)):F(K,ie+z)},U=0,V,G,Y=function(z){U++;var K="audioTrackSet",H="autoGainControl",re="echoCancellation",ie="noiseSuppression",q=K+":{"+re+","+ie+","+H+"}",J=JSON.parse(i(v[K]||!0));f.CLog("open... "+U+" "+K+":"+i(J)),z&&(typeof J!="object"&&(J={}),J[H]=!1,J[re]=!1,J[ie]=!1),J[y]&&f.CLog($("IjL3::注意：已配置{1}参数，可能会出现浏览器不能正确选用麦克风、移动端无法启用回声消除等现象",0,K+"."+y),3);var ee={audio:J,video:v.videoTrackSet||!1};try{var de=o.Scope[d](ee,O,N)}catch(fe){f.CLog(d,3,fe),ee={audio:!0,video:!1},de=o.Scope[d](ee,O,N)}f.CLog(d+"("+i(ee)+") "+D(A)+$("RiWe::，未配置 {1} 时浏览器可能会自动启用回声消除，移动端未禁用回声消除时可能会降低系统播放音量（关闭录音后可恢复）和仅提供16k采样率的音频流（不需要回声消除时可明确配置成禁用来获得48k高音质的流），请参阅文档中{2}配置",0,q,K)+"("+a+") LM:"+c+" UA:"+navigator.userAgent),de&&de.then&&de.then(O)[L](N)};Y()},close:function(g){g=g||r;var u=this,f=u._streamStore();u._stop();var v=" stream:"+u._streamTag,x=f.Sync;if(u._O=0,u._O_!=x.O){u.CLog($("hWVz::close被忽略（因为同时open了多个rec，只有最后一个会真正close）")+v,3),g();return}x.C++,Ne(f),u.CLog("close,"+v),g()},mock:function(g,u){var f=this;return f._stop(),f.isMock=1,f.mockEnvInfo=null,f.buffers=[g],f.recSize=g.length,f._setSrcSR(u),f._streamTag="mock",f},_setSrcSR:function(g){var u=this,f=u.set,v=f[y];v>g?f[y]=g:v=0,u[_]=g,u.CLog(_+": "+g+" set."+y+": "+f[y]+(v?" "+$("UHvm::忽略")+": "+v:""),v?3:0)},envCheck:function(g){var u,f=this,v=f.set,x="CPU_BE";if(!u&&!o[x]&&typeof Int8Array=="function"&&!new Int8Array(new Int32Array([1]).buffer)[0]&&(Rt(x),u=$("Essp::不支持{1}架构",0,x)),!u){var T=v.type,l=f[T+"_envCheck"];v.takeoffEncodeChunk&&(l?g.canProcess||(u=$("7uMV::{1}环境不支持实时处理",0,g.envName)):u=$("2XBl::{1}类型不支持设置takeoffEncodeChunk",0,T)+(f[T]?"":$("LG7e::(未加载编码器)"))),!u&&l&&(u=f[T+"_envCheck"](g,v))}return u||""},envStart:function(g,u){var f=this,v=f.set;if(f.isMock=g?1:0,f.mockEnvInfo=g,f.buffers=[],f.recSize=0,g&&(f._streamTag="env$"+g.envName),f.state=1,f.envInLast=0,f.envInFirst=0,f.envInFix=0,f.envInFixTs=[],f._setSrcSR(u),f.engineCtx=0,f[v.type+"_start"]){var x=f.engineCtx=f[v.type+"_start"](v);x&&(x.pcmDatas=[],x.pcmSize=0)}},envResume:function(){this.envInFixTs=[]},envIn:function(g,u){var f=this,v=f.set,x=f.engineCtx;if(f.state!=1){f.state||f.CLog("envIn at state=0",3);return}var T=f[_],l=g.length,h=o.PowerLevel(u,l),m=f.buffers,w=m.length;m.push(g);var S=m,E=w,P=Date.now(),A=Math.round(l/T*1e3);f.envInLast=P,f.buffers.length==1&&(f.envInFirst=P-A);var C=f.envInFixTs;C.splice(0,0,{t:P,d:A});for(var R=P,F=0,M=0;M<C.length;M++){var O=C[M];if(P-O.t>3e3){C.length=M;break}R=O.t,F+=O.d}var N=C[1],U=P-R,V=U-F;if(V>U/3&&(N&&U>1e3||C.length>=6)){var G=P-N.t-A;if(G>A/5){var Y=!v.disableEnvInFix;if(f.CLog("["+P+"]"+_e.get($(Y?"4Kfd::补偿{1}ms":"bM5i::未补偿{1}ms",1),[G]),3),f.envInFix+=G,Y){var z=new Int16Array(G*T/1e3);l+=z.length,m.push(z)}}}var K=f.recSize,H=l,re=K+H;if(f.recSize=re,x){var ie=o.SampleData(m,T,v[y],x.chunkInfo);x.chunkInfo=ie,K=x.pcmSize,H=ie.data.length,re=K+H,x.pcmSize=re,m=x.pcmDatas,w=m.length,m.push(ie.data),T=ie[y]}var q=Math.round(re/T*1e3),J=m.length,ee=S.length,de=function(){for(var pe=fe?0:-H,Pe=m[0]==null,ae=w;ae<J;ae++){var Re=m[ae];Re==null?Pe=1:(pe+=Re.length,x&&Re.length&&f[v.type+"_encode"](x,Re))}if(Pe&&x){var ae=E;for(S[0]&&(ae=0);ae<ee;ae++)S[ae]=null}Pe&&(pe=fe?H:0,m[0]=null),x?x.pcmSize+=pe:f.recSize+=pe},fe=0,ce="rec.set.onProcess";try{fe=v.onProcess(m,h,q,T,w,de),fe=fe===!0}catch(pe){console.error(ce+$("gFUF::回调出错是不允许的，需保证不会抛异常"),pe)}var be=Date.now()-P;if(be>10&&f.envInFirst-P>1e3&&f.CLog(ce+$("2ghS::低性能，耗时{1}ms",0,be),3),fe){for(var ue=0,M=w;M<J;M++)m[M]==null?ue=1:m[M]=new Int16Array(0);ue?f.CLog($("ufqH::未进入异步前不能清除buffers"),3):x?x.pcmSize-=H:f.recSize-=H}else de()},start:function(){var g=this,u=1;if(g.set.sourceStream?g.Stream||(u=0):o.IsOpen()||(u=0),!u){g.CLog($("6WmN::start失败：未open"),1);return}var f=g._streamCtx();if(g.CLog($("kLDN::start 开始录音，")+D(f)+" stream:"+g._streamTag),g._stop(),g.envStart(null,f[y]),g.state=3,g._SO&&g._SO+1!=g._S){g.CLog($("Bp2y::start被中断"),3);return}g._SO=0;var v=function(){g.state==3&&(g.state=1,g.resume())},x="AudioContext resume: ",T=g._streamGet();T._call[g.id]=function(){g.CLog(x+f.state+"|stream ok"),v()},j(f,function(l){return l&&g.CLog(x+"wait..."),g.state==3},function(l){l&&g.CLog(x+f.state),v()},function(l){g.CLog(x+f.state+$("upkE::，可能无法录音：")+l,1),v()})},pause:function(){var g=this,u=g._streamGet();g.state&&(g.state=2,g.CLog("pause"),u&&delete u._call[g.id])},resume:function(){var g=this,u=g._streamGet(),f="resume",v=f+"(wait ctx)";if(g.state==3)g.CLog(v);else if(g.state){g.state=1,g.CLog(f),g.envResume(),u&&(u._call[g.id]=function(T,l){g.state==1&&g.envIn(T,l)},Be(u));var x=g._streamCtx();x&&j(x,function(T){return T&&g.CLog(v+"..."),g.state==1},function(T){T&&g.CLog(v+x.state),Be(u)},function(T){g.CLog(v+x.state+"[err]"+T,1)})}},_stop:function(g){var u=this,f=u.set;u.isMock||u._S++,u.state&&(u.pause(),u.state=0),!g&&u[f.type+"_stop"]&&(u[f.type+"_stop"](u.engineCtx),u.engineCtx=0)},stop:function(g,u,f){var v=this,x=v.set,T,l=v.envInLast-v.envInFirst,h=l&&v.buffers.length;v.CLog($("Xq4s::stop 和start时差:")+(l?l+"ms "+$("3CQP::补偿:")+v.envInFix+"ms envIn:"+h+" fps:"+(h/l*1e3).toFixed(1):"-")+" stream:"+v._streamTag+" ("+a+") LM:"+c);var m=function(){v._stop(),f&&v.close()},w=function(O){v.CLog($("u8JG::结束录音失败：")+O,1),u&&u(O),m()},S=function(O,N,U){var V="blob",G="arraybuffer",Y="dataType",z="DefaultDataType",K=v[Y]||o[z]||V,H=Y+"="+K,re=O instanceof ArrayBuffer,ie=0,q=re?O.byteLength:O.size;if(K==G?re||(ie=1):K==V?typeof Blob!="function"?ie=$.G("NonBrowser-1",[H])+$("1skY::，请设置{1}",0,p+"."+z+'="'+G+'"'):(re&&(O=new Blob([O],{type:N})),O instanceof Blob||(ie=1),N=O.type||N):ie=$.G("NotSupport-1",[H]),v.CLog($("Wv7l::结束录音 编码花{1}ms 音频时长{2}ms 文件大小{3}b",0,Date.now()-T,U,q)+" "+H+","+N),ie){w(ie!=1?ie:$("Vkbd::{1}编码器返回的不是{2}",0,x.type,K)+", "+H);return}if(x.takeoffEncodeChunk)v.CLog($("QWnr::启用takeoffEncodeChunk后stop返回的blob长度为0不提供音频数据"),3);else if(q<Math.max(50,U/5)){w($("Sz2H::生成的{1}无效",0,x.type));return}g&&g(O,U,N),m()};if(!v.isMock){var E=v.state==3;if(!v.state||E){w($("wf9t::未开始录音")+(E?$("Dl2c::，开始录音前无用户交互导致AudioContext未运行"):""));return}}v._stop(!0);var P=v.recSize;if(!P){w($("Ltz3::未采集到录音"));return}if(!v[x.type]){w($("xGuI::未加载{1}编码器，请尝试到{2}的src/engine内找到{1}的编码器并加载",0,x.type,p));return}if(v.isMock){var A=v.envCheck(v.mockEnvInfo||{envName:"mock",canProcess:!1});if(A){w($("AxOH::录音错误：")+A);return}}var C=v.engineCtx;if(v[x.type+"_complete"]&&C){var M=Math.round(C.pcmSize/x[y]*1e3);T=Date.now(),v[x.type+"_complete"](C,function(N,U){S(N,U,M)},w);return}if(T=Date.now(),!v.buffers[0]){w($("xkKd::音频buffers被释放"));return}var R=o.SampleData(v.buffers,v[_],x[y]);x[y]=R[y];var F=R.data,M=Math.round(F.length/x[y]*1e3);v.CLog($("CxeT::采样:{1} 花:{2}ms",0,P+"->"+F.length,Date.now()-T)),setTimeout(function(){T=Date.now(),v[x.type](F,function(O,N){S(O,N,M)},function(O){w(O)})})}};var ne=function(g,u){u.pos||(u.pos=[0],u.tracks={},u.bytes=[]);var f=u.tracks,v=[u.pos[0]],x=function(){u.pos[0]=v[0]},T=u.bytes.length,l=new Uint8Array(T+g.length);if(l.set(u.bytes),l.set(g,T),u.bytes=l,!u._ht){if(Ce(l,v),xe(l,v),!we(Ce(l,v),[24,83,128,103]))return;for(Ce(l,v);v[0]<l.length;){var h=Ce(l,v),m=xe(l,v),w=[0],S=0;if(!m)return;if(we(h,[22,84,174,107])){for(;w[0]<m.length;){var E=Ce(m,w),P=xe(m,w),A=[0],C={channels:0,sampleRate:0};if(we(E,[174]))for(;A[0]<P.length;){var R=Ce(P,A),F=xe(P,A),M=[0];if(we(R,[215])){var O=ke(F);C.number=O,f[O]=C}else if(we(R,[131])){var O=ke(F);O==1?C.type="video":O==2?(C.type="audio",S||(u.track0=C),C.idx=S++):C.type="Type-"+O}else if(we(R,[134])){for(var N="",U=0;U<F.length;U++)N+=String.fromCharCode(F[U]);C.codec=N}else if(we(R,[225]))for(;M[0]<F.length;){var V=Ce(F,M),G=xe(F,M);if(we(V,[181])){var O=0,Y=new Uint8Array(G.reverse()).buffer;G.length==4?O=new Float32Array(Y)[0]:G.length==8?O=new Float64Array(Y)[0]:X("WebM Track !Float",1,G),C[y]=Math.round(O)}else we(V,[98,100])?C.bitDepth=ke(G):we(V,[159])&&(C.channels=ke(G))}}}u._ht=1,X("WebM Tracks",f),x();break}}}var z=u.track0;if(z){var K=z[y];if(u.webmSR=K,z.bitDepth==16&&/FLOAT/i.test(z.codec)&&(z.bitDepth=32,X("WebM 16->32 bit",3)),K<8e3||z.bitDepth!=32||z.channels<1||!/(\b|_)PCM\b/i.test(z.codec))return u.bytes=[],u.bad||X("WebM Track Unexpected",3,u),u.bad=1,-1;for(var H=[],re=0;v[0]<l.length;){var E=Ce(l,v),P=xe(l,v);if(!P)break;if(we(E,[163])){var ie=P[0]&15,C=f[ie];if(C){if(C.idx===0){for(var q=new Uint8Array(P.length-4),U=4;U<P.length;U++)q[U-4]=P[U];H.push(q),re+=q.length}}else return X("WebM !Track"+ie,1,f),-1}x()}if(re){var J=new Uint8Array(l.length-u.pos[0]);J.set(l.subarray(u.pos[0])),u.bytes=J,u.pos[0]=0;for(var q=new Uint8Array(re),U=0,ee=0;U<H.length;U++)q.set(H[U],ee),ee+=H[U].length;var Y=new Float32Array(q.buffer);if(z.channels>1){for(var de=[],U=0;U<Y.length;)de.push(Y[U]),U+=z.channels;Y=new Float32Array(de)}return Y}}},we=function(g,u){if(!g||g.length!=u.length)return!1;if(g.length==1)return g[0]==u[0];for(var f=0;f<g.length;f++)if(g[f]!=u[f])return!1;return!0},ke=function(g){for(var u="",f=0;f<g.length;f++){var v=g[f];u+=(v<16?"0":"")+v.toString(16)}return parseInt(u,16)||0},Ce=function(g,u,f){var v=u[0];if(!(v>=g.length)){var x=g[v],T=("0000000"+x.toString(2)).substr(-8),l=/^(0*1)(\d*)$/.exec(T);if(l){var h=l[1].length,m=[];if(!(v+h>g.length)){for(var w=0;w<h;w++)m[w]=g[v],v++;return f&&(m[0]=parseInt(l[2]||"0",2)),u[0]=v,m}}}},xe=function(g,u){var f=Ce(g,u,1);if(f){var v=ke(f),x=u[0],T=[];if(v<2147483647){if(x+v>g.length)return;for(var l=0;l<v;l++)T[l]=g[x],x++}return u[0]=x,T}},_e=o.i18n={lang:"zh-CN",alias:{"zh-CN":"zh","en-US":"en"},locales:{},data:{},put:function(g,u){var f=p+".i18n.put: ",v=g.overwrite;v=v==null||v;var x=g.lang;if(x=_e.alias[x]||x,!x)throw new Error(f+"set.lang?");var T=_e.locales[x];T||(T={},_e.locales[x]=T);for(var l=/^([\w\-]+):/,h,m=0;m<u.length;m++){var S=u[m];if(h=l.exec(S),!h){X(f+"'key:'? "+S,3,g);continue}var w=h[1],S=S.substr(w.length+1);!v&&T[w]||(T[w]=S)}},get:function(){return _e.v_G.apply(null,arguments)},v_G:function(g,u,f){u=u||[],f=f||_e.lang,f=_e.alias[f]||f;var v=_e.locales[f],x=v&&v[g]||"";return!x&&f!="zh"?f=="en"?_e.v_G(g,u,"zh"):_e.v_G(g,u,"en"):(_e.lastLang=f,x=="=Empty"?"":x.replace(/\{(\d+)(\!?)\}/g,function(T,l,h){return l=+l||0,T=u[l-1],(l<1||l>u.length)&&(T="{?}",X("i18n["+g+"] no {"+l+"}: "+x,3)),h?"":T}))},$T:function(){return _e.v_T.apply(null,arguments)},v_T:function(){for(var g=arguments,u="",f=[],v=0,x=p+".i18n.$T:",T=/^([\w\-]*):/,l,h=0;h<g.length;h++){var m=g[h];if(h==0){if(l=T.exec(m),u=l&&l[1],!u)throw new Error(x+"0 'key:'?");m=m.substr(u.length+1)}if(v===-1)f.push(m);else{if(v)throw new Error(x+" bad args");if(m===0)v=-1;else if(s(m)){if(m<1)throw new Error(x+" bad args");v=m}else{var w=h==1?"en":h?"":"zh";if(l=T.exec(m),l&&(w=l[1]||w,m=m.substr(l[1].length+1)),!l||!w)throw new Error(x+h+" 'lang:'?");_e.put({lang:w,overwrite:!1},[u+":"+m])}}}return u?v>0?u:_e.v_G(u,f):""}},$=_e.$T;$.G=_e.get,$("NonBrowser-1::非浏览器环境，不支持{1}",1),$("IllegalArgs-1::参数错误：{1}",1),$("NeedImport-2::调用{1}需要先导入{2}",2),$("NotSupport-1::不支持：{1}",1),o.TrafficImgUrl="//ia.51.la/go1?id=20469973&pvFlag=1";var Rt=o.Traffic=function(g){if(n){g=g?"/"+p+"/Report/"+g:"";var u=o.TrafficImgUrl;if(u){var f=o.Traffic,v=/^(https?:..[^\/#]*\/?)[^#]*/i.exec(location.href)||[],x=v[1]||"http://file/",T=(v[0]||x)+g;if(u.indexOf("//")==0&&(/^https:/i.test(T)?u="https:"+u:u="http:"+u),g&&(u=u+"&cu="+encodeURIComponent(x+g)),!f[T]){f[T]=1;var l=new Image;l.src=u,X("Traffic Analysis Image: "+(g||p+".TrafficImgUrl="+o.TrafficImgUrl))}}}};I&&(X($("8HO5::覆盖导入{1}",0,p),1),I.Destroy()),t[p]=o})})(lr)),lr.exports}var aa=ca();const cr=la(aa);(function(e){var t=typeof window=="object"&&!!window.document,n=t?window:Object,r=n.Recorder,s=r.i18n;e(r,s,s.$T,t)})(function(e,t,n,r){e.prototype.enc_wav={stable:!0,fast:!0,getTestMsg:function(){return n("gPSE::支持位数8位、16位（填在比特率里面），采样率取值无限制；此编码器仅在pcm数据前加了一个44字节的wav头，编码出来的16位wav文件去掉开头的44字节即可得到pcm（注：其他wav编码器可能不是44字节）")}};var s=function(i){var o=i.bitRate,c=o==8?8:16;o!=c&&e.CLog(n("wyw9::WAV Info: 不支持{1}位，已更新成{2}位",0,o,c),3),i.bitRate=c};e.prototype.wav=function(i,o,c){var a=this,p=a.set;s(p);var d=i.length,_=p.sampleRate,y=p.bitRate,b=d*(y/8),L=e.wav_header(1,1,_,y,b),I=L.length,B=new Uint8Array(I+b);if(B.set(L),y==8)for(var k=0;k<d;k++){var j=(i[k]>>8)+128;B[I++]=j}else B=new Int16Array(B.buffer),B.set(i,I/2);o(B.buffer,"audio/wav")},e.wav_header=function(i,o,c,a,p){var d=i==1?0:2,_=new ArrayBuffer(44+d),y=new DataView(_),b=0,L=function(k){for(var j=0;j<k.length;j++,b++)y.setUint8(b,k.charCodeAt(j))},I=function(k){y.setUint16(b,k,!0),b+=2},B=function(k){y.setUint32(b,k,!0),b+=4};return L("RIFF"),B(36+d+p),L("WAVE"),L("fmt "),B(16+d),I(i),I(o),B(c),B(c*(o*a/8)),I(o*a/8),I(a),i!=1&&I(0),L("data"),B(p),new Uint8Array(_)}});(function(e){var t=typeof window=="object"&&!!window.document,n=t?window:Object,r=n.Recorder,s=r.i18n;e(r,s,s.$T,t)})(function(e,t,n,r){var s=function(c){return new o(c)},i="WaveView",o=function(c){var a=this,p={scale:2,speed:9,phase:21.8,fps:20,keep:!0,lineWidth:3,linear1:[0,"rgba(150,96,238,1)",.2,"rgba(170,79,249,1)",1,"rgba(53,199,253,1)"],linear2:[0,"rgba(209,130,255,0.6)",1,"rgba(53,199,255,0.6)"],linearBg:[0,"rgba(255,255,255,0.2)",1,"rgba(54,197,252,0.2)"]};for(var d in c)p[d]=c[d];a.set=c=p;var _="compatibleCanvas";if(c[_])var y=a.canvas=c[_];else{if(!r)throw new Error(n.G("NonBrowser-1",[i]));var b=c.elem;b&&(typeof b=="string"?b=document.querySelector(b):b.length&&(b=b[0])),b&&(c.width=b.offsetWidth,c.height=b.offsetHeight);var L=a.elem=document.createElement("div");L.style.fontSize=0,L.innerHTML='<canvas style="width:100%;height:100%;"/>';var y=a.canvas=L.querySelector("canvas");b&&(b.innerHTML="",b.appendChild(L))}var I=c.scale,B=c.width*I,k=c.height*I;if(!B||!k)throw new Error(n.G("IllegalArgs-1",[i+" width=0 height=0"]));y.width=B,y.height=k;var j=a.ctx=y.getContext("2d");a.linear1=a.genLinear(j,B,c.linear1),a.linear2=a.genLinear(j,B,c.linear2),a.linearBg=a.genLinear(j,k,c.linearBg,!0),a._phase=0};o.prototype=s.prototype={genLinear:function(c,a,p,d){for(var _=c.createLinearGradient(0,0,d?0:a,d?a:0),y=0;y<p.length;)_.addColorStop(p[y++],p[y++]);return _},genPath:function(c,a,p){for(var d=[],_=this,y=_.set,b=y.scale,L=y.width*b,I=y.height*b/2,B=0;B<=L;B+=b){var k=(1+Math.cos(Math.PI+B/L*2*Math.PI))/2,j=k*I*a*Math.sin(2*Math.PI*(B/L)*c+p)+I;d.push(j)}return d},input:function(c,a,p){var d=this;d.sampleRate=p,d.pcmData=c,d.pcmPos=0,d.inputTime=Date.now(),d.schedule()},schedule:function(){var c=this,a=c.set,p=Math.floor(1e3/a.fps);c.timer||(c.timer=setInterval(function(){c.schedule()},p));var d=Date.now(),_=c.drawTime||0;if(!(d-_<p)){c.drawTime=d;for(var y=c.sampleRate/a.fps,b=c.pcmData,L=c.pcmPos,I=Math.max(0,Math.min(y,b.length-L)),B=0,k=0;k<I;k++,L++)B+=Math.abs(b[L]);c.pcmPos=L,(I||!a.keep)&&c.draw(e.PowerLevel(B,I)),!I&&d-c.inputTime>1300&&(clearInterval(c.timer),c.timer=0)}},draw:function(c){var a=this,p=a.set,d=a.ctx,_=p.scale,y=p.width*_,b=p.height*_,L=p.speed/p.fps,I=a._phase-=L,B=I+L*p.phase,k=c/100,j=a.genPath(2,k,I),W=a.genPath(1.8,k,B);d.clearRect(0,0,y,b),d.beginPath();for(var D=0,Q=0;Q<=y;D++,Q+=_)Q==0?d.moveTo(Q,j[D]):d.lineTo(Q,j[D]);D--;for(var Q=y-1;Q>=0;D--,Q-=_)d.lineTo(Q,W[D]);d.closePath(),d.fillStyle=a.linearBg,d.fill(),a.drawPath(W,a.linear2),a.drawPath(j,a.linear1)},drawPath:function(c,a){var p=this,d=p.set,_=p.ctx,y=d.scale,b=d.width*y;_.beginPath();for(var L=0,I=0;I<=b;L++,I+=y)I==0?_.moveTo(I,c[L]):_.lineTo(I,c[L]);_.lineWidth=d.lineWidth*y,_.strokeStyle=a,_.stroke()}},e[i]=s});var Je=(e=>(e.STOPPED="stopped",e.RECORDING="recording",e.PAUSED="paused",e.ERROR="error",e))(Je||{});function fa(e){let t=null,n=new Int16Array;const r=It(Je.STOPPED);It(0);async function s(){try{return await navigator.mediaDevices.getUserMedia({audio:{channelCount:1,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}})}catch(y){return console.error("获取音频流失败:",y),null}}const i=async y=>{t&&(t.close(),t=null);try{const b=await s();if(!b)throw r.value=Je.ERROR,alert("无法获取音频流"),new Error("无法获取音频流");t=new cr({type:"wav",sampleRate:44100,bitRate:256,onProcess:p,sourceStream:b,audioTrackSet:{channelCount:1,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}}),t.open(()=>{console.log("录音初始化成功"),y&&y()},L=>{console.error("录音初始化失败:",L);const I={type:"wav",sampleRate:16e3,bitRate:16,onProcess:p};t=new cr(I),t&&t.open(y,B=>{console.error("录音完全初始化失败:",B)})})}catch(b){throw console.error("录音设备初始化失败:",b),b}},o=()=>{t&&t.start()},c=y=>{n.length>0&&(n=new Int16Array),t&&t.stop((b,L)=>{y&&y(b,L)}),r.value=Je.STOPPED},a=async()=>{try{await i(()=>{console.log(1),o(),r.value=Je.RECORDING})}catch(y){console.error("打开录音失败:",y),r.value=Je.STOPPED}};function p(y,b,L,I){if(r.value===Je.RECORDING){const B=y[y.length-1],k=new Array(B),j=cr.SampleData(k,I,16e3).data;n=Int16Array.from([...n,...j]);const W=960;for(;n.length>=W;)n.slice(0,W),n=n.slice(W,n.length)}}function d(){r.value=Je.PAUSED,t&&t.pause()}function _(){console.log("恢复录音"),r.value=Je.RECORDING,t==null||t.resume()}return{audioState:r,recOpen:a,recStop:c,recPause:d,recResume:_}}const ua={class:"audio-recorder"},ha={class:"recorder-container"},da=["disabled"],pa={key:0,class:"record-icon"},ga={key:1,class:"stop-icon"},va={key:2,class:"pulse"},ma={key:0,class:"duration"},_a={class:"status-text"},ya={key:0},ba={key:1},wa={key:2},xa={key:0,class:"error-message"},Sa=Gn({__name:"AudioRecorder",emits:["recordingComplete","recordingStart","recordingError"],setup(e,{emit:t}){const n=t,r=It(""),s=It(0),i=It(null),{audioState:o,recOpen:c,recStop:a}=fa(),p=ze(()=>o.value===Je.RECORDING),d=ze(()=>o.value===Je.ERROR),_=W=>{const D=Math.floor(W/60),Q=W%60;return`${D.toString().padStart(2,"0")}:${Q.toString().padStart(2,"0")}`},y=()=>{s.value=0,i.value=window.setInterval(()=>{s.value++},1e3)},b=()=>{i.value&&(clearInterval(i.value),i.value=null)},L=async()=>{try{r.value="",await c(),y(),n("recordingStart"),console.log("录音开始")}catch(W){const D=W instanceof Error?W.message:"录音启动失败";r.value=D,n("recordingError",D),console.error("录音启动失败:",W)}},I=()=>{b(),a((W,D)=>{n("recordingComplete",W,D)})},B=()=>{p.value?I():L()},k=()=>{window.electronAPI?window.electronAPI.minimizeWindow():console.log("最小化功能需要在 Electron 环境中使用")},j=()=>{window.electronAPI?window.electronAPI.closeWindow():console.log("关闭功能需要在 Electron 环境中使用")};return Ur(()=>{b()}),(W,D)=>(qe(),tt("div",ua,[Ee("div",{class:"title-bar"},[D[2]||(D[2]=Ee("h1",{class:"title"},"录音器",-1)),Ee("div",null,[Ee("button",{class:"minimize-btn",onClick:k,title:"最小化到桌面组件"},D[0]||(D[0]=[Ee("svg",{viewBox:"0 0 24 24",class:"icon"},[Ee("path",{d:"M19 13H5v-2h14v2z"})],-1)])),Ee("button",{class:"close-btn",onClick:j,title:"关闭"},D[1]||(D[1]=[Ee("svg",{viewBox:"0 0 24 24",class:"icon"},[Ee("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})],-1)]))])]),Ee("div",ha,[Ee("button",{class:Wn(["record-button",{recording:p.value,disabled:d.value}]),onClick:B,disabled:d.value},[p.value?(qe(),tt("div",ga,D[4]||(D[4]=[Ee("svg",{viewBox:"0 0 24 24",fill:"currentColor"},[Ee("rect",{x:"6",y:"6",width:"12",height:"12",rx:"2"})],-1)]))):(qe(),tt("div",pa,D[3]||(D[3]=[Ee("svg",{viewBox:"0 0 24 24",fill:"currentColor"},[Ee("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),Ee("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"})],-1)]))),p.value?(qe(),tt("div",va)):Rn("",!0)],10,da),p.value?(qe(),tt("div",ma,_r(_(s.value)),1)):Rn("",!0)]),Ee("div",_a,[!p.value&&!d.value?(qe(),tt("span",ya,"点击开始录音")):d.value?(qe(),tt("span",ba,"正在初始化...")):p.value?(qe(),tt("span",wa,"点击停止")):Rn("",!0)]),r.value?(qe(),tt("div",xa,_r(r.value),1)):Rn("",!0)]))}}),Ca=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Ea=Ca(Sa,[["__scopeId","data-v-bb8db41c"]]),Ra={class:"app"},Ta=Gn({__name:"App",setup(e){const t=(s,i)=>{console.log("录音完成回调:",{size:s.size,type:s.type,duration:i}),console.log(s),alert("录音完成")},n=()=>{console.log("录音开始")},r=s=>{console.error("录音错误:",s)};return(s,i)=>(qe(),tt("div",Ra,[Fe(Ea,{onRecordingComplete:t,onRecordingStart:n,onRecordingError:r})]))}});/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Bt=typeof document<"u";function to(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Pa(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&to(e.default)}const oe=Object.assign;function ar(e,t){const n={};for(const r in t){const s=t[r];n[r]=Xe(s)?s.map(e):e(s)}return n}const ln=()=>{},Xe=Array.isArray,no=/#/g,Aa=/&/g,Ma=/\//g,Oa=/=/g,Ia=/\?/g,ro=/\+/g,La=/%5B/g,Da=/%5D/g,so=/%5E/g,Na=/%60/g,io=/%7B/g,$a=/%7C/g,oo=/%7D/g,ka=/%20/g;function Kr(e){return encodeURI(""+e).replace($a,"|").replace(La,"[").replace(Da,"]")}function Fa(e){return Kr(e).replace(io,"{").replace(oo,"}").replace(so,"^")}function Er(e){return Kr(e).replace(ro,"%2B").replace(ka,"+").replace(no,"%23").replace(Aa,"%26").replace(Na,"`").replace(io,"{").replace(oo,"}").replace(so,"^")}function ja(e){return Er(e).replace(Oa,"%3D")}function Ba(e){return Kr(e).replace(no,"%23").replace(Ia,"%3F")}function Ha(e){return e==null?"":Ba(e).replace(Ma,"%2F")}function gn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Ua=/\/$/,Wa=e=>e.replace(Ua,"");function fr(e,t,n="/"){let r,s={},i="",o="";const c=t.indexOf("#");let a=t.indexOf("?");return c<a&&c>=0&&(a=-1),a>-1&&(r=t.slice(0,a),i=t.slice(a+1,c>-1?c:t.length),s=e(i)),c>-1&&(r=r||t.slice(0,c),o=t.slice(c,t.length)),r=Ka(r??t,n),{fullPath:r+(i&&"?")+i+o,path:r,query:s,hash:gn(o)}}function za(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ps(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ga(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&Vt(t.matched[r],n.matched[s])&&lo(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Vt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function lo(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Va(e[n],t[n]))return!1;return!0}function Va(e,t){return Xe(e)?As(e,t):Xe(t)?As(t,e):e===t}function As(e,t){return Xe(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Ka(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let i=n.length-1,o,c;for(o=0;o<r.length;o++)if(c=r[o],c!==".")if(c==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+r.slice(o).join("/")}const mt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var vn;(function(e){e.pop="pop",e.push="push"})(vn||(vn={}));var cn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(cn||(cn={}));function qa(e){if(!e)if(Bt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Wa(e)}const Ja=/^[^#]+#/;function Qa(e,t){return e.replace(Ja,"#")+t}function Ya(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Jn=()=>({left:window.scrollX,top:window.scrollY});function Xa(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Ya(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ms(e,t){return(history.state?history.state.position-t:-1)+e}const Rr=new Map;function Za(e,t){Rr.set(e,t)}function ef(e){const t=Rr.get(e);return Rr.delete(e),t}let tf=()=>location.protocol+"//"+location.host;function co(e,t){const{pathname:n,search:r,hash:s}=t,i=e.indexOf("#");if(i>-1){let c=s.includes(e.slice(i))?e.slice(i).length:1,a=s.slice(c);return a[0]!=="/"&&(a="/"+a),Ps(a,"")}return Ps(n,e)+r+s}function nf(e,t,n,r){let s=[],i=[],o=null;const c=({state:y})=>{const b=co(e,location),L=n.value,I=t.value;let B=0;if(y){if(n.value=b,t.value=y,o&&o===L){o=null;return}B=I?y.position-I.position:0}else r(b);s.forEach(k=>{k(n.value,L,{delta:B,type:vn.pop,direction:B?B>0?cn.forward:cn.back:cn.unknown})})};function a(){o=n.value}function p(y){s.push(y);const b=()=>{const L=s.indexOf(y);L>-1&&s.splice(L,1)};return i.push(b),b}function d(){const{history:y}=window;y.state&&y.replaceState(oe({},y.state,{scroll:Jn()}),"")}function _(){for(const y of i)y();i=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",d)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",d,{passive:!0}),{pauseListeners:a,listen:p,destroy:_}}function Os(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Jn():null}}function rf(e){const{history:t,location:n}=window,r={value:co(e,n)},s={value:t.state};s.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(a,p,d){const _=e.indexOf("#"),y=_>-1?(n.host&&document.querySelector("base")?e:e.slice(_))+a:tf()+e+a;try{t[d?"replaceState":"pushState"](p,"",y),s.value=p}catch(b){console.error(b),n[d?"replace":"assign"](y)}}function o(a,p){const d=oe({},t.state,Os(s.value.back,a,s.value.forward,!0),p,{position:s.value.position});i(a,d,!0),r.value=a}function c(a,p){const d=oe({},s.value,t.state,{forward:a,scroll:Jn()});i(d.current,d,!0);const _=oe({},Os(r.value,a,null),{position:d.position+1},p);i(a,_,!1),r.value=a}return{location:r,state:s,push:c,replace:o}}function sf(e){e=qa(e);const t=rf(e),n=nf(e,t.state,t.location,t.replace);function r(i,o=!0){o||n.pauseListeners(),history.go(i)}const s=oe({location:"",base:e,go:r,createHref:Qa.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function of(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),sf(e)}function lf(e){return typeof e=="string"||e&&typeof e=="object"}function ao(e){return typeof e=="string"||typeof e=="symbol"}const fo=Symbol("");var Is;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Is||(Is={}));function Kt(e,t){return oe(new Error,{type:e,[fo]:!0},t)}function ct(e,t){return e instanceof Error&&fo in e&&(t==null||!!(e.type&t))}const Ls="[^/]+?",cf={sensitive:!1,strict:!1,start:!0,end:!0},af=/[.+*?^${}()[\]/\\]/g;function ff(e,t){const n=oe({},cf,t),r=[];let s=n.start?"^":"";const i=[];for(const p of e){const d=p.length?[]:[90];n.strict&&!p.length&&(s+="/");for(let _=0;_<p.length;_++){const y=p[_];let b=40+(n.sensitive?.25:0);if(y.type===0)_||(s+="/"),s+=y.value.replace(af,"\\$&"),b+=40;else if(y.type===1){const{value:L,repeatable:I,optional:B,regexp:k}=y;i.push({name:L,repeatable:I,optional:B});const j=k||Ls;if(j!==Ls){b+=10;try{new RegExp(`(${j})`)}catch(D){throw new Error(`Invalid custom RegExp for param "${L}" (${j}): `+D.message)}}let W=I?`((?:${j})(?:/(?:${j}))*)`:`(${j})`;_||(W=B&&p.length<2?`(?:/${W})`:"/"+W),B&&(W+="?"),s+=W,b+=20,B&&(b+=-8),I&&(b+=-20),j===".*"&&(b+=-50)}d.push(b)}r.push(d)}if(n.strict&&n.end){const p=r.length-1;r[p][r[p].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const o=new RegExp(s,n.sensitive?"":"i");function c(p){const d=p.match(o),_={};if(!d)return null;for(let y=1;y<d.length;y++){const b=d[y]||"",L=i[y-1];_[L.name]=b&&L.repeatable?b.split("/"):b}return _}function a(p){let d="",_=!1;for(const y of e){(!_||!d.endsWith("/"))&&(d+="/"),_=!1;for(const b of y)if(b.type===0)d+=b.value;else if(b.type===1){const{value:L,repeatable:I,optional:B}=b,k=L in p?p[L]:"";if(Xe(k)&&!I)throw new Error(`Provided param "${L}" is an array but it is not repeatable (* or + modifiers)`);const j=Xe(k)?k.join("/"):k;if(!j)if(B)y.length<2&&(d.endsWith("/")?d=d.slice(0,-1):_=!0);else throw new Error(`Missing required param "${L}"`);d+=j}}return d||"/"}return{re:o,score:r,keys:i,parse:c,stringify:a}}function uf(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function uo(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const i=uf(r[n],s[n]);if(i)return i;n++}if(Math.abs(s.length-r.length)===1){if(Ds(r))return 1;if(Ds(s))return-1}return s.length-r.length}function Ds(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const hf={type:0,value:""},df=/[a-zA-Z0-9_]/;function pf(e){if(!e)return[[]];if(e==="/")return[[hf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(b){throw new Error(`ERR (${n})/"${p}": ${b}`)}let n=0,r=n;const s=[];let i;function o(){i&&s.push(i),i=[]}let c=0,a,p="",d="";function _(){p&&(n===0?i.push({type:0,value:p}):n===1||n===2||n===3?(i.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:p,regexp:d,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),p="")}function y(){p+=a}for(;c<e.length;){if(a=e[c++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(p&&_(),o()):a===":"?(_(),n=1):y();break;case 4:y(),n=r;break;case 1:a==="("?n=2:df.test(a)?y():(_(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&c--);break;case 2:a===")"?d[d.length-1]=="\\"?d=d.slice(0,-1)+a:n=3:d+=a;break;case 3:_(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&c--,d="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${p}"`),_(),o(),s}function gf(e,t,n){const r=ff(pf(e.path),n),s=oe(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function vf(e,t){const n=[],r=new Map;t=Fs({strict:!1,end:!0,sensitive:!1},t);function s(_){return r.get(_)}function i(_,y,b){const L=!b,I=$s(_);I.aliasOf=b&&b.record;const B=Fs(t,_),k=[I];if("alias"in _){const D=typeof _.alias=="string"?[_.alias]:_.alias;for(const Q of D)k.push($s(oe({},I,{components:b?b.record.components:I.components,path:Q,aliasOf:b?b.record:I})))}let j,W;for(const D of k){const{path:Q}=D;if(y&&Q[0]!=="/"){const ye=y.record.path,he=ye[ye.length-1]==="/"?"":"/";D.path=y.record.path+(Q&&he+Q)}if(j=gf(D,y,B),b?b.alias.push(j):(W=W||j,W!==j&&W.alias.push(j),L&&_.name&&!ks(j)&&o(_.name)),ho(j)&&a(j),I.children){const ye=I.children;for(let he=0;he<ye.length;he++)i(ye[he],j,b&&b.children[he])}b=b||j}return W?()=>{o(W)}:ln}function o(_){if(ao(_)){const y=r.get(_);y&&(r.delete(_),n.splice(n.indexOf(y),1),y.children.forEach(o),y.alias.forEach(o))}else{const y=n.indexOf(_);y>-1&&(n.splice(y,1),_.record.name&&r.delete(_.record.name),_.children.forEach(o),_.alias.forEach(o))}}function c(){return n}function a(_){const y=yf(_,n);n.splice(y,0,_),_.record.name&&!ks(_)&&r.set(_.record.name,_)}function p(_,y){let b,L={},I,B;if("name"in _&&_.name){if(b=r.get(_.name),!b)throw Kt(1,{location:_});B=b.record.name,L=oe(Ns(y.params,b.keys.filter(W=>!W.optional).concat(b.parent?b.parent.keys.filter(W=>W.optional):[]).map(W=>W.name)),_.params&&Ns(_.params,b.keys.map(W=>W.name))),I=b.stringify(L)}else if(_.path!=null)I=_.path,b=n.find(W=>W.re.test(I)),b&&(L=b.parse(I),B=b.record.name);else{if(b=y.name?r.get(y.name):n.find(W=>W.re.test(y.path)),!b)throw Kt(1,{location:_,currentLocation:y});B=b.record.name,L=oe({},y.params,_.params),I=b.stringify(L)}const k=[];let j=b;for(;j;)k.unshift(j.record),j=j.parent;return{name:B,path:I,params:L,matched:k,meta:_f(k)}}e.forEach(_=>i(_));function d(){n.length=0,r.clear()}return{addRoute:i,resolve:p,removeRoute:o,clearRoutes:d,getRoutes:c,getRecordMatcher:s}}function Ns(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function $s(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:mf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function mf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function ks(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _f(e){return e.reduce((t,n)=>oe(t,n.meta),{})}function Fs(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function yf(e,t){let n=0,r=t.length;for(;n!==r;){const i=n+r>>1;uo(e,t[i])<0?r=i:n=i+1}const s=bf(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function bf(e){let t=e;for(;t=t.parent;)if(ho(t)&&uo(e,t)===0)return t}function ho({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function wf(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const i=r[s].replace(ro," "),o=i.indexOf("="),c=gn(o<0?i:i.slice(0,o)),a=o<0?null:gn(i.slice(o+1));if(c in t){let p=t[c];Xe(p)||(p=t[c]=[p]),p.push(a)}else t[c]=a}return t}function js(e){let t="";for(let n in e){const r=e[n];if(n=ja(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Xe(r)?r.map(i=>i&&Er(i)):[r&&Er(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function xf(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Xe(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Sf=Symbol(""),Bs=Symbol(""),qr=Symbol(""),po=Symbol(""),Tr=Symbol("");function Qt(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function bt(e,t,n,r,s,i=o=>o()){const o=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((c,a)=>{const p=y=>{y===!1?a(Kt(4,{from:n,to:t})):y instanceof Error?a(y):lf(y)?a(Kt(2,{from:t,to:y})):(o&&r.enterCallbacks[s]===o&&typeof y=="function"&&o.push(y),c())},d=i(()=>e.call(r&&r.instances[s],t,n,p));let _=Promise.resolve(d);e.length<3&&(_=_.then(p)),_.catch(y=>a(y))})}function ur(e,t,n,r,s=i=>i()){const i=[];for(const o of e)for(const c in o.components){let a=o.components[c];if(!(t!=="beforeRouteEnter"&&!o.instances[c]))if(to(a)){const d=(a.__vccOpts||a)[t];d&&i.push(bt(d,n,r,o,c,s))}else{let p=a();i.push(()=>p.then(d=>{if(!d)throw new Error(`Couldn't resolve component "${c}" at "${o.path}"`);const _=Pa(d)?d.default:d;o.mods[c]=d,o.components[c]=_;const b=(_.__vccOpts||_)[t];return b&&bt(b,n,r,o,c,s)()}))}}return i}function Hs(e){const t=dt(qr),n=dt(po),r=ze(()=>{const a=Ht(e.to);return t.resolve(a)}),s=ze(()=>{const{matched:a}=r.value,{length:p}=a,d=a[p-1],_=n.matched;if(!d||!_.length)return-1;const y=_.findIndex(Vt.bind(null,d));if(y>-1)return y;const b=Us(a[p-2]);return p>1&&Us(d)===b&&_[_.length-1].path!==b?_.findIndex(Vt.bind(null,a[p-2])):y}),i=ze(()=>s.value>-1&&Pf(n.params,r.value.params)),o=ze(()=>s.value>-1&&s.value===n.matched.length-1&&lo(n.params,r.value.params));function c(a={}){if(Tf(a)){const p=t[Ht(e.replace)?"replace":"push"](Ht(e.to)).catch(ln);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}return{route:r,href:ze(()=>r.value.href),isActive:i,isExactActive:o,navigate:c}}function Cf(e){return e.length===1?e[0]:e}const Ef=Gn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Hs,setup(e,{slots:t}){const n=Bn(Hs(e)),{options:r}=dt(qr),s=ze(()=>({[Ws(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Ws(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&Cf(t.default(n));return e.custom?i:Ji("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},i)}}}),Rf=Ef;function Tf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Pf(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Xe(s)||s.length!==r.length||r.some((i,o)=>i!==s[o]))return!1}return!0}function Us(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ws=(e,t,n)=>e??t??n,Af=Gn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=dt(Tr),s=ze(()=>e.route||r.value),i=dt(Bs,0),o=ze(()=>{let p=Ht(i);const{matched:d}=s.value;let _;for(;(_=d[p])&&!_.components;)p++;return p}),c=ze(()=>s.value.matched[o.value]);Tn(Bs,ze(()=>o.value+1)),Tn(Sf,c),Tn(Tr,s);const a=It();return Pn(()=>[a.value,c.value,e.name],([p,d,_],[y,b,L])=>{d&&(d.instances[_]=p,b&&b!==d&&p&&p===y&&(d.leaveGuards.size||(d.leaveGuards=b.leaveGuards),d.updateGuards.size||(d.updateGuards=b.updateGuards))),p&&d&&(!b||!Vt(d,b)||!y)&&(d.enterCallbacks[_]||[]).forEach(I=>I(p))},{flush:"post"}),()=>{const p=s.value,d=e.name,_=c.value,y=_&&_.components[d];if(!y)return zs(n.default,{Component:y,route:p});const b=_.props[d],L=b?b===!0?p.params:typeof b=="function"?b(p):b:null,B=Ji(y,oe({},L,t,{onVnodeUnmounted:k=>{k.component.isUnmounted&&(_.instances[d]=null)},ref:a}));return zs(n.default,{Component:B,route:p})||B}}});function zs(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Mf=Af;function Of(e){const t=vf(e.routes,e),n=e.parseQuery||wf,r=e.stringifyQuery||js,s=e.history,i=Qt(),o=Qt(),c=Qt(),a=Ko(mt);let p=mt;Bt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const d=ar.bind(null,u=>""+u),_=ar.bind(null,Ha),y=ar.bind(null,gn);function b(u,f){let v,x;return ao(u)?(v=t.getRecordMatcher(u),x=f):x=u,t.addRoute(x,v)}function L(u){const f=t.getRecordMatcher(u);f&&t.removeRoute(f)}function I(){return t.getRoutes().map(u=>u.record)}function B(u){return!!t.getRecordMatcher(u)}function k(u,f){if(f=oe({},f||a.value),typeof u=="string"){const m=fr(n,u,f.path),w=t.resolve({path:m.path},f),S=s.createHref(m.fullPath);return oe(m,w,{params:y(w.params),hash:gn(m.hash),redirectedFrom:void 0,href:S})}let v;if(u.path!=null)v=oe({},u,{path:fr(n,u.path,f.path).path});else{const m=oe({},u.params);for(const w in m)m[w]==null&&delete m[w];v=oe({},u,{params:_(m)}),f.params=_(f.params)}const x=t.resolve(v,f),T=u.hash||"";x.params=d(y(x.params));const l=za(r,oe({},u,{hash:Fa(T),path:x.path})),h=s.createHref(l);return oe({fullPath:l,hash:T,query:r===js?xf(u.query):u.query||{}},x,{redirectedFrom:void 0,href:h})}function j(u){return typeof u=="string"?fr(n,u,a.value.path):oe({},u)}function W(u,f){if(p!==u)return Kt(8,{from:f,to:u})}function D(u){return he(u)}function Q(u){return D(oe(j(u),{replace:!0}))}function ye(u){const f=u.matched[u.matched.length-1];if(f&&f.redirect){const{redirect:v}=f;let x=typeof v=="function"?v(u):v;return typeof x=="string"&&(x=x.includes("?")||x.includes("#")?x=j(x):{path:x},x.params={}),oe({query:u.query,hash:u.hash,params:x.path!=null?{}:u.params},x)}}function he(u,f){const v=p=k(u),x=a.value,T=u.state,l=u.force,h=u.replace===!0,m=ye(v);if(m)return he(oe(j(m),{state:typeof m=="object"?oe({},T,m.state):T,force:l,replace:h}),f||v);const w=v;w.redirectedFrom=f;let S;return!l&&Ga(r,x,v)&&(S=Kt(16,{to:w,from:x}),Ce(x,x,!0,!1)),(S?Promise.resolve(S):He(w,x)).catch(E=>ct(E)?ct(E,2)?E:ke(E):ne(E,w,x)).then(E=>{if(E){if(ct(E,2))return he(oe({replace:h},j(E.to),{state:typeof E.to=="object"?oe({},T,E.to.state):T,force:l}),f||w)}else E=Ze(w,x,!0,h,T);return Ne(w,x,E),E})}function Be(u,f){const v=W(u,f);return v?Promise.reject(v):Promise.resolve()}function Ke(u){const f=$.values().next().value;return f&&typeof f.runWithContext=="function"?f.runWithContext(u):u()}function He(u,f){let v;const[x,T,l]=If(u,f);v=ur(x.reverse(),"beforeRouteLeave",u,f);for(const m of x)m.leaveGuards.forEach(w=>{v.push(bt(w,u,f))});const h=Be.bind(null,u,f);return v.push(h),g(v).then(()=>{v=[];for(const m of i.list())v.push(bt(m,u,f));return v.push(h),g(v)}).then(()=>{v=ur(T,"beforeRouteUpdate",u,f);for(const m of T)m.updateGuards.forEach(w=>{v.push(bt(w,u,f))});return v.push(h),g(v)}).then(()=>{v=[];for(const m of l)if(m.beforeEnter)if(Xe(m.beforeEnter))for(const w of m.beforeEnter)v.push(bt(w,u,f));else v.push(bt(m.beforeEnter,u,f));return v.push(h),g(v)}).then(()=>(u.matched.forEach(m=>m.enterCallbacks={}),v=ur(l,"beforeRouteEnter",u,f,Ke),v.push(h),g(v))).then(()=>{v=[];for(const m of o.list())v.push(bt(m,u,f));return v.push(h),g(v)}).catch(m=>ct(m,8)?m:Promise.reject(m))}function Ne(u,f,v){c.list().forEach(x=>Ke(()=>x(u,f,v)))}function Ze(u,f,v,x,T){const l=W(u,f);if(l)return l;const h=f===mt,m=Bt?history.state:{};v&&(x||h?s.replace(u.fullPath,oe({scroll:h&&m&&m.scroll},T)):s.push(u.fullPath,T)),a.value=u,Ce(u,f,v,h),ke()}let $e;function X(){$e||($e=s.listen((u,f,v)=>{if(!Rt.listening)return;const x=k(u),T=ye(x);if(T){he(oe(T,{replace:!0,force:!0}),x).catch(ln);return}p=x;const l=a.value;Bt&&Za(Ms(l.fullPath,v.delta),Jn()),He(x,l).catch(h=>ct(h,12)?h:ct(h,2)?(he(oe(j(h.to),{force:!0}),x).then(m=>{ct(m,20)&&!v.delta&&v.type===vn.pop&&s.go(-1,!1)}).catch(ln),Promise.reject()):(v.delta&&s.go(-v.delta,!1),ne(h,x,l))).then(h=>{h=h||Ze(x,l,!1),h&&(v.delta&&!ct(h,8)?s.go(-v.delta,!1):v.type===vn.pop&&ct(h,20)&&s.go(-1,!1)),Ne(x,l,h)}).catch(ln)}))}let ot=Qt(),Se=Qt(),se;function ne(u,f,v){ke(u);const x=Se.list();return x.length?x.forEach(T=>T(u,f,v)):console.error(u),Promise.reject(u)}function we(){return se&&a.value!==mt?Promise.resolve():new Promise((u,f)=>{ot.add([u,f])})}function ke(u){return se||(se=!u,X(),ot.list().forEach(([f,v])=>u?v(u):f()),ot.reset()),u}function Ce(u,f,v,x){const{scrollBehavior:T}=e;if(!Bt||!T)return Promise.resolve();const l=!v&&ef(Ms(u.fullPath,0))||(x||!v)&&history.state&&history.state.scroll||null;return yi().then(()=>T(u,f,l)).then(h=>h&&Xa(h)).catch(h=>ne(h,u,f))}const xe=u=>s.go(u);let _e;const $=new Set,Rt={currentRoute:a,listening:!0,addRoute:b,removeRoute:L,clearRoutes:t.clearRoutes,hasRoute:B,getRoutes:I,resolve:k,options:e,push:D,replace:Q,go:xe,back:()=>xe(-1),forward:()=>xe(1),beforeEach:i.add,beforeResolve:o.add,afterEach:c.add,onError:Se.add,isReady:we,install(u){const f=this;u.component("RouterLink",Rf),u.component("RouterView",Mf),u.config.globalProperties.$router=f,Object.defineProperty(u.config.globalProperties,"$route",{enumerable:!0,get:()=>Ht(a)}),Bt&&!_e&&a.value===mt&&(_e=!0,D(s.location).catch(T=>{}));const v={};for(const T in mt)Object.defineProperty(v,T,{get:()=>a.value[T],enumerable:!0});u.provide(qr,f),u.provide(po,oi(v)),u.provide(Tr,a);const x=u.unmount;$.add(u),u.unmount=function(){$.delete(u),$.size<1&&(p=mt,$e&&$e(),$e=null,a.value=mt,_e=!1,se=!1),x()}}};function g(u){return u.reduce((f,v)=>f.then(()=>Ke(v)),Promise.resolve())}return Rt}function If(e,t){const n=[],r=[],s=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const c=t.matched[o];c&&(e.matched.find(p=>Vt(p,c))?r.push(c):n.push(c));const a=e.matched[o];a&&(t.matched.find(p=>Vt(p,a))||s.push(a))}return[n,r,s]}const Lf=Of({history:of("./"),routes:[]}),Jr=na(Ta);Jr.use(oa());Jr.use(Lf);Jr.mount("#app");
