/*!
 * pinia v3.0.1
 * (c) 2025 <PERSON>
 * @license MIT
 */
var Pinia=function(t,e){"use strict";let n;const o=t=>n=t,i=Symbol();function c(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var s;t.MutationType=void 0,(s=t.MutationType||(t.MutationType={})).direct="direct",s.patchObject="patch object",s.patchFunction="patch function";const r=()=>{};function a(t,n,o,i=r){t.push(n);const c=()=>{const e=t.indexOf(n);e>-1&&(t.splice(e,1),i())};return!o&&e.getCurrentScope()&&e.onScopeDispose(c),c}function u(t,...e){t.slice().forEach((t=>{t(...e)}))}const f=t=>t(),p=Symbol(),l=Symbol();function h(t,n){t instanceof Map&&n instanceof Map?n.forEach(((e,n)=>t.set(n,e))):t instanceof Set&&n instanceof Set&&n.forEach(t.add,t);for(const o in n){if(!n.hasOwnProperty(o))continue;const i=n[o],s=t[o];t[o]=c(s)&&c(i)&&t.hasOwnProperty(o)&&!e.isRef(i)&&!e.isReactive(i)?h(s,i):i}return t}const y=Symbol();function d(t){return!c(t)||!t.hasOwnProperty(y)}const{assign:v}=Object;function b(n,i,c={},s,y,b){let $;const _=v({actions:{}},c),S={deep:!0};let j,m,O,R=[],g=[];const P=s.state.value[n];let A;function M(o){let i;j=m=!1,"function"==typeof o?(o(s.state.value[n]),i={type:t.MutationType.patchFunction,storeId:n,events:O}):(h(s.state.value[n],o),i={type:t.MutationType.patchObject,payload:o,storeId:n,events:O});const c=A=Symbol();e.nextTick().then((()=>{A===c&&(j=!0)})),m=!0,u(R,i,s.state.value[n])}b||P||(s.state.value[n]={}),e.ref({});const w=b?function(){const{state:t}=c,e=t?t():{};this.$patch((t=>{v(t,e)}))}:r;const k=(t,e="")=>{if(p in t)return t[l]=e,t;const i=function(){o(s);const e=Array.from(arguments),c=[],r=[];let a;u(g,{args:e,name:i[l],store:x,after:function(t){c.push(t)},onError:function(t){r.push(t)}});try{a=t.apply(this&&this.$id===n?this:x,e)}catch(t){throw u(r,t),t}return a instanceof Promise?a.then((t=>(u(c,t),t))).catch((t=>(u(r,t),Promise.reject(t)))):(u(c,a),a)};return i[p]=!0,i[l]=e,i},T={_p:s,$id:n,$onAction:a.bind(null,g),$patch:M,$reset:w,$subscribe(o,i={}){const c=a(R,o,i.detached,(()=>r())),r=$.run((()=>e.watch((()=>s.state.value[n]),(e=>{("sync"===i.flush?m:j)&&o({storeId:n,type:t.MutationType.direct,events:O},e)}),v({},S,i))));return c},$dispose:function(){$.stop(),R=[],g=[],s._s.delete(n)}},x=e.reactive(T);s._s.set(n,x);const E=(s._a&&s._a.runWithContext||f)((()=>s._e.run((()=>($=e.effectScope()).run((()=>i({action:k})))))));for(const t in E){const o=E[t];if(e.isRef(o)&&(!e.isRef(I=o)||!I.effect)||e.isReactive(o))b||(P&&d(o)&&(e.isRef(o)?o.value=P[t]:h(o,P[t])),s.state.value[n][t]=o);else if("function"==typeof o){const e=k(o,t);E[t]=e,_.actions[t]=o}}var I;return v(x,E),v(e.toRaw(x),E),Object.defineProperty(x,"$state",{get:()=>s.state.value[n],set:t=>{M((e=>{v(e,t)}))}}),s._p.forEach((t=>{v(x,$.run((()=>t({store:x,app:s._a,pinia:s,options:_}))))})),P&&b&&c.hydrate&&c.hydrate(x.$state,P),j=!0,m=!0,x}
/*! #__NO_SIDE_EFFECTS__ */let $="Store";function _(t,e){return Array.isArray(e)?e.reduce(((e,n)=>(e[n]=function(){return t(this.$pinia)[n]},e)),{}):Object.keys(e).reduce(((n,o)=>(n[o]=function(){const n=t(this.$pinia),i=e[o];return"function"==typeof i?i.call(this,n):n[i]},n)),{})}const S=_;return t.acceptHMRUpdate=function(t,e){return()=>{}},t.createPinia=function(){const t=e.effectScope(!0),n=t.run((()=>e.ref({})));let c=[],s=[];const r=e.markRaw({install(t){o(r),r._a=t,t.provide(i,r),t.config.globalProperties.$pinia=r,s.forEach((t=>c.push(t))),s=[]},use(t){return this._a?c.push(t):s.push(t),this},_p:c,_a:null,_e:t,_s:new Map,state:n});return r},t.defineStore=function(t,c,s){let r;const a="function"==typeof c;function u(s,u){const f=e.hasInjectionContext();(s=s||(f?e.inject(i,null):null))&&o(s),(s=n)._s.has(t)||(a?b(t,c,r,s):function(t,n,i){const{state:c,actions:s,getters:r}=n,a=i.state.value[t];let u;u=b(t,(function(){a||(i.state.value[t]=c?c():{});const n=e.toRefs(i.state.value[t]);return v(n,s,Object.keys(r||{}).reduce(((n,c)=>(n[c]=e.markRaw(e.computed((()=>{o(i);const e=i._s.get(t);return r[c].call(e,e)}))),n)),{}))}),n,i,0,!0)}(t,r,s));return s._s.get(t)}return r=a?s:c,u.$id=t,u},t.disposePinia=function(t){t._e.stop(),t._s.clear(),t._p.splice(0),t.state.value={},t._a=null},t.getActivePinia=()=>e.hasInjectionContext()&&e.inject(i)||n,t.mapActions=function(t,e){return Array.isArray(e)?e.reduce(((e,n)=>(e[n]=function(...e){return t(this.$pinia)[n](...e)},e)),{}):Object.keys(e).reduce(((n,o)=>(n[o]=function(...n){return t(this.$pinia)[e[o]](...n)},n)),{})},t.mapGetters=S,t.mapState=_,t.mapStores=function(...t){return t.reduce(((t,e)=>(t[e.$id+$]=function(){return e(this.$pinia)},t)),{})},t.mapWritableState=function(t,e){return Array.isArray(e)?e.reduce(((e,n)=>(e[n]={get(){return t(this.$pinia)[n]},set(e){return t(this.$pinia)[n]=e}},e)),{}):Object.keys(e).reduce(((n,o)=>(n[o]={get(){return t(this.$pinia)[e[o]]},set(n){return t(this.$pinia)[e[o]]=n}},n)),{})},t.setActivePinia=o,t.setMapStoreSuffix=function(t){$=t},t.shouldHydrate=d,t.skipHydrate=function(t){return Object.defineProperty(t,y,{})},t.storeToRefs=function(t){const n=e.toRaw(t),o={};for(const i in n){const c=n[i];c.effect?o[i]=e.computed({get:()=>t[i],set(e){t[i]=e}}):(e.isRef(c)||e.isReactive(c))&&(o[i]=e.toRef(t,i))}return o},t}({},Vue);
