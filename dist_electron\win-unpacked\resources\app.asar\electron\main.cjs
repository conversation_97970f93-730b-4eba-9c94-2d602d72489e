const { app, globalShortcut, ipcMain, BrowserWindow, screen, Tray, Menu } = require('electron')
const os = require('os')

const path = require('path')
const dotenv = require('dotenv')

dotenv.config()

// 设置开机自启动
app.setLoginItemSettings({
  openAtLogin: true,
  path: process.execPath,
})

const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
  // 如果我们无法获取单实例锁，则退出当前实例
  app.quit()
} else {
  app.on('second-instance', () => {
    // 当运行第二个实例时，我们应该聚焦到主窗口
    if (win) {
      if (win.isMinimized()) win.restore()
      win.focus()
    }
  })
}
let win
let tray

let miniWin = null // 小窗口实例

const createWindow = async () => {
  // 创建主窗口
  win = new BrowserWindow({
    width: 300,
    height: 300,
    frame: false, // 有边框
    titleBarStyle: 'default',
    resizable: false, // 可调整大小但有限制
    maximizable: true, // 不允许最大化
    fullscreenable: true, // 不允许全屏
    transparent: true, // 透明背景
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  })
  // 移除默认菜单
  Menu.setApplicationMenu(null)

  win.setMenu(null) // 去除菜单

  // 使窗口可拖动
  win.setMovable(true)

  // 在Electron的主进程中
  const isDev = process.env.ELECTRON_IS_DEV === 'true'
  console.log('isDev', isDev)
  if (isDev) {
    // 开发环境配置
    win.loadURL(process.env.VITE_DEV_SERVER_URL)
    // win.webContents.openDevTools() // 关闭开发者工具
  } else {
    // 生产环境配置
    win.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // 窗口最小化时创建桌面小图标
  win.on('minimize', (event) => {
    event.preventDefault()
    // createMiniWindow()
    win.hide()
  })

  // 阻止窗口关闭，改为隐藏到系统托盘
  win.on('close', (event) => {
    event.preventDefault()
    win.hide()
    // if (!miniWin) {
    //   createMiniWindow()
    // }
  })
}

// 创建桌面小图标窗口
const createMiniWindow = () => {
  if (miniWin) {
    miniWin.show()
    return
  }

  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize

  miniWin = new BrowserWindow({
    width: 400,
    height: 200,
    x: screenWidth - 140, // 距离右边20px
    y: screenHeight - 140, // 距离底部20px
    frame: false, // 无边框
    transparent: true, // 透明背景
    alwaysOnTop: true, // 始终置顶
    resizable: true, // 不可调整大小
    skipTaskbar: true, // 不在任务栏显示
    webPreferences: {
      preload: path.join(__dirname, 'preload.cjs'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  })

  miniWin.setMenu(null)

  // 加载同样的页面，但会根据窗口大小显示不同的界面
  const isDev = process.env.ELECTRON_IS_DEV === 'true'
  if (isDev) {
    miniWin.loadURL(process.env.VITE_DEV_SERVER_URL + '?mini=true')
  } else {
    miniWin.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // 双击小窗口恢复主窗口
  miniWin.on('double-click', () => {
    restoreMainWindow()
  })

  // 小窗口关闭时隐藏到托盘
  miniWin.on('close', (event) => {
    event.preventDefault()
    miniWin.hide()
  })
}

// 恢复主窗口
const restoreMainWindow = () => {
  if (miniWin) {
    miniWin.hide()
  }
  if (win) {
    win.show()
    win.focus()
  }
}

// IPC通信处理
ipcMain.handle('minimize-window', () => {
  if (win) {
    win.minimize()
  }
})

ipcMain.handle('close-window', () => {
  if (win) {
    win.close() // 关闭窗口
  }
})

ipcMain.handle('show-window', () => {
  restoreMainWindow()
})

ipcMain.handle('minimize-to-desktop', () => {
  // createMiniWindow()
  if (win) {
    win.hide()
  }
})

ipcMain.handle('restore-main-window', () => {
  restoreMainWindow()
})

ipcMain.handle('move-window', (event, deltaX, deltaY) => {
  if (win) {
    const [currentX, currentY] = win.getPosition()
    win.setPosition(currentX + deltaX, currentY + deltaY)
  }
})

// 创建系统托盘
const createTray = () => {
  tray = new Tray(path.join(__dirname, '../build/icons/icon.ico'))

  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示录音器',
      click: () => {
        if (win) {
          win.show()
          win.focus()
        }
      },
    },
    {
      label: '退出',
      click: () => {
        app.quit()
      },
    },
  ])

  tray.setToolTip('录音器')
  tray.setContextMenu(contextMenu)

  // 双击托盘图标显示窗口
  tray.on('double-click', () => {
    if (win) {
      win.show()
      win.focus()
    }
  })
}

app.whenReady().then(() => {
  createWindow()
  createTray()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('ready', () => {
  // 创建 Electron 窗口等其他操作
  globalShortcut.register('Alt+CommandOrControl+I', () => {
    BrowserWindow.getFocusedWindow()?.webContents.openDevTools()
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})
